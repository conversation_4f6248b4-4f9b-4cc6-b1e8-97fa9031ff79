import { useEffect } from 'react'
import { <PERSON><PERSON><PERSON>Router } from 'react-router'
import { AppRoutes } from './routes'
import cookie from 'cookiejs'
import { ACCESS_TOKEN, REFRESH_TOKEN } from './constants'
import { ConfigProvider, type ThemeConfig } from 'antd'
import '@/src/config/translation'
import dayjs from 'dayjs'
import 'dayjs/locale/vi'
import 'dayjs/locale/en'
import { useAppSelector } from './hooks/useAppSelector'
dayjs.locale('en')

const theme: ThemeConfig = {
  token: {
    colorError: '#e93c3c',
    colorPrimary: '#006885',
  },
  components: {
    Button: {
      colorPrimary: '#006885',
      algorithm: false,
    },
    Table: {
      stickyScrollBarBg: '#006885',
    },
  },
}

function App() {
  const { locale } = useAppSelector(state => state.global)

  useEffect(() => {
    const handleBeforeUnload = () => {
      // <PERSON><PERSON><PERSON> tra nếu là refresh thì không xóa cookie
      const navEntries = performance.getEntriesByType(
        'navigation'
      ) as PerformanceNavigationTiming[]
      const firstEntry = navEntries[0]
      if (firstEntry?.type === 'reload') {
        return
      }

      const rememberMe = JSON.parse(
        localStorage.getItem('rememberMe') || 'false'
      )
      if (!rememberMe) {
        cookie.remove(ACCESS_TOKEN)
        cookie.remove(REFRESH_TOKEN)
        localStorage.removeItem('rememberMe')
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
    }
  }, [])

  return (
    <ConfigProvider theme={theme} locale={locale}>
      <BrowserRouter>
        <AppRoutes />
      </BrowserRouter>
    </ConfigProvider>
  )
}

export default App
