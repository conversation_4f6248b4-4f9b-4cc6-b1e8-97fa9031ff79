import type { DatePickerProps } from 'antd'
import type { Dayjs } from 'dayjs'

const getYearMonth = (date: Dayjs) => date.year() * 12 + date.month()

const disabledDaysDate = (days: number): DatePickerProps['disabledDate'] => {
  return (current, { from, type }) => {
    if (from) {
      const minDate = from.add(-(days - 1), 'days')
      const maxDate = from.add(days - 1, 'days')

      switch (type) {
        case 'year':
          return (
            current.year() < minDate.year() || current.year() > maxDate.year()
          )

        case 'month':
          return (
            getYearMonth(current) < getYearMonth(minDate) ||
            getYearMonth(current) > getYearMonth(maxDate)
          )

        default:
          return Math.abs(current.diff(from, 'days')) >= days
      }
    }

    return false
  }
}
export { disabledDaysDate }
