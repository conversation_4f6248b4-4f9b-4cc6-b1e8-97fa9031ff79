/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import i18n from '@/src/config/translation'
import { AIRPORT_GLOBAL } from '@/src/constants'
import { selectFilterOption } from '@/src/helper/selectFilterOption'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import { getAirportByUser } from '@/src/service/airport'
import { getMyProfile, logout } from '@/src/service/auth'
import { signOut } from '@/src/store/AuthSlice'
import {
  setAirportGlobal,
  setLocale,
  setProfile,
} from '@/src/store/GlobalSlice'
import { LogoutOutlined, UserOutlined } from '@ant-design/icons'
import { useQuery } from '@tanstack/react-query'
import type { MenuProps } from 'antd'
import { Avatar, Dropdown, Select, Space } from 'antd'
import dayjs from 'dayjs'
import { useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router'

const HeaderComponent = () => {
  const dispatch = useAppDispatch()
  const { t } = useTranslation()
  const navigate = useNavigate()
  const query = useQuery({
    queryKey: ['logout'],
    queryFn: logout,
    enabled: false,
  })

  const { airportGlobal } = useAppSelector(state => state.global)

  const { data } = useQuery({
    queryKey: ['my-profile'],
    queryFn: () => getMyProfile(),
  })

  const { data: airportData } = useQuery({
    queryKey: ['airport-list-by-user'],
    queryFn: () => getAirportByUser(),
  })

  useEffect(() => {
    dispatch(setProfile(data))
  }, [data])

  const items: MenuProps['items'] = [
    {
      key: 'sign-out',
      label: t('common.signout'),
      icon: <LogoutOutlined />,
      onClick: async () => {
        await query.refetch()
        dispatch(signOut())
        navigate('/login-ldap')
        dispatch(setAirportGlobal(''))
      },
    },
  ]

  return (
    <div className="pb-4 flex items-center w-full justify-end">
      <Space>
        {!airportGlobal && (
          <span className="text-secondary">Airport not selected</span>
        )}
        <Select
          className="w-32"
          placeholder={t('pts.airport')}
          value={airportGlobal}
          showSearch
          filterOption={selectFilterOption}
          options={
            airportData?.map((item: any) => ({
              value: item.iataCode,
              label: item.iataCode,
            })) || []
          }
          onSelect={value => {
            localStorage.setItem(AIRPORT_GLOBAL, value)
            dispatch(setAirportGlobal(value))
          }}
        />
        <Select
          className="w-32"
          options={[
            { label: t('basic.vietnamese'), value: 'vi' },
            { label: t('basic.english'), value: 'en' },
          ]}
          onChange={value => {
            i18n.changeLanguage(value)
            dayjs.locale(value)
            dispatch(setLocale(value as 'en' | 'vi'))
          }}
          value={i18n.language}
        />
        <Dropdown
          menu={{ items }}
          placement="bottomRight"
          trigger={['click']}
          popupRender={() => (
            <div className="flex flex-col shadow-lg p-4 rounded-lg w-48 gap-y-2 bg-white">
              <div className="flex flex-row gap-x-4 border-b pb-2 border-slate-200">
                <Avatar size={24} icon={<UserOutlined />} />
                <div className="text-sm">{data?.name}</div>
              </div>
              {items.map((item: any) => (
                <div
                  key={item.key}
                  className="text-sm font-normal cursor-pointer gap-x-4 flex flex-row items-center hover:bg-gray-100 rounded-md p-1"
                  onClick={item.onClick}
                >
                  {item.icon} {item.label}
                </div>
              ))}
            </div>
          )}
        >
          <Avatar size={32} icon={<UserOutlined />} />
        </Dropdown>
      </Space>
    </div>
  )
}

export default HeaderComponent
