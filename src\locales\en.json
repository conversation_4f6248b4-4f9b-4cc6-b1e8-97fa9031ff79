{"basic": {"fromDate": "From date", "toDate": "To date", "vietnamese": "Vietnamese", "english": "English", "hour": "hour", "minute": "minute", "second": "second"}, "common": {"add": "Add new", "edit": "Edit", "delete": "Delete", "save": "Save", "cancel": "Cancel", "search": "Search", "sort": "Sort", "signout": "Sign out", "apply": "Apply", "filter": "Filter", "all": "All", "total": "Total", "items": "items", "action": "Action", "confirm": "Confirm", "confirmDeleteTitle": "Confirm delete", "confirmDeleteMessage": "Are you sure you want to delete this item?", "yes": "Yes", "no": "No", "copy": "Copy", "notPermission": "You don't have permission to perform this action", "type": "type", "viewColumn": "Select display columns", "selected": "Selected", "record": "records", "from": "From", "to": "to", "in": "of total", "backToHome": "Back to home", "notFound": "The page you are looking for does not exist", "actual": "Actual", "plan": "Plan", "line": "Line", "last": "Last", "late": "Late", "onTime": "On time", "early": "Early", "detail": "Detail", "close": "Close", "update": "Update", "reply": "Reply", "print": "Print", "messageBy": "Messaged by", "at": "at", "createBreifing": "Create a briefing", "create": "Create", "addMore": "Add more", "report": "Report", "export": "Export", "saveandsendViags": "Save and send to Viags", "reportName": "Baocao", "selectAirport": "Select airport", "deleteThisReason": "Delete this reason?", "demodata": "The current values are mock data for testing purposes", "noData": "No data found", "approvedBy": "Approved by"}, "nav": {"home": "Home", "category": "Category", "categoryPTS": "Standard PTS", "edit": "Edit", "create": "Add new", "categoryJobs": "Job milestone", "airport": "Airport", "configuration": {"_": "Configuration", "email": "Email configuration"}, "evaluationRatioAdjustment": {"_": "Evaluation ratio adjustment", "selfAssessment": "Self-assessment", "employeesToBeEvaluated": "List of employees to be evaluated", "performanceEvaluationSummary": "Job performance evaluation summary"}, "evaluationRatio": {"_": "Evaluation ratio", "evaluationTargetRatio": "Regulatory evaluation ratio", "actualEvaluationRatio": "Actual evaluation ratio"}, "jobPerformanceEvaluation": {"_": "Job performance evaluation", "selfAssessment": "Self-assessment", "performanceEvaluationSummary": "Job performance evaluation summary"}, "evaluationApproval": {"_": "Job performance evaluation approval", "selfAssessment": "Self-assessment", "approval": {"_": "Job performance evaluation approval", "departmentEvaluationApproval": "Department job performance evaluation approval", "centerEvaluationApproval": "Center job performance evaluation approval"}}, "directorEvaluationApproval": {"_": "Job performance evaluation approval", "departmentEvaluationApproval": "Department job performance evaluation approval", "centerEvaluationApproval": "Center job performance evaluation approval"}, "productivitySalaryManagement": "Productivity salary management", "regulationRatioDeclaration": "Regulatory ratio declaration", "flightSchedule": "Flight schedule", "departure": "Departure schedule", "arrival": "Arrival schedule", "permissionManagement": "Permission management", "employeeManagement": "Employee management", "administrator": "Administrator", "roleManagement": "Role management", "userManagement": "User management", "OrganizationUnits": "Organization unit management", "workPerformanceEvaluationFormManagement": "Job performance evaluation form management", "organizationalStructureManagement": "Organizational structure management", "adjustRatingScale": "Adjust evaluation ratio", "aircraftTechInfo": "Aircraft Tech Info", "groupPtsTask": "Job milestone group", "copy": "Copy", "fleet": "Aircraft", "listFlightOTP": "List of flights not meeting OTP", "listFlightOSP": "List of flights not meeting OSP", "report": "Report", "reportGroupTask": "List of job milestone groups serving late", "reportFlight": "List of delayed flights", "reportDepartment": "List of units serving late", "flightLate": "Delayed flights", "departmentLate": "Units serving late", "groupTaskLate": "Job milestone groups serving late", "flightOnTimeGT": "Flights serving on-time GT", "flightLateGT": "Flights serving late GT", "reportPTSTaskLate": "PTS task serving late", "reportNoRecordPTS": "Flights not recording PTS", "general": "General schedule"}, "table": {"order": "No.", "status": "Status", "description": "Description", "createdAt": "Created date", "updatedAt": "Updated date", "action": "Action", "aircraft": "Aircraft", "reason": "Reason", "ptsTask": "Job milestone", "estimateTime": "Estimate time", "actualTime": "Actual time", "delayTime": "Delay time", "flightNo": "Flight no", "routing": "Routing", "ptsGroupTask": "Job milestone group", "department": "Unit", "overTimeCount": "Over time count", "totalTimeDelay": "Total delay time", "estimateGT": "Estimate GT", "actualGT": "Actual GT", "name": "Name", "cost": "Cost", "choose": "<PERSON><PERSON>"}, "flight": {"flightSchedule": "Flight schedule", "departureSchedule": "Departure schedule", "flightDetails": "Flight details"}, "permission": {"permissionList": "Permission list", "permissionGroupName": "Permission group name", "numberOfAccounts": "Number of accounts"}, "job": {"jobCode": "Job milestone code", "jobName": "Job milestone name - Vietnamese", "jobNameEng": "Job milestone name - English", "jobStatus": "Status", "jobCreatedDate": "Created date", "jobAction": "Action", "jobList": "Job milestone list", "jobCreate": "Create new job milestone", "jobFilter": "Filter", "jobSearchPlaceholder": "Search job milestone", "jobUpdate": "Edit", "jobDetail": "Detail", "jobInfoTitle": "Create new job milestone", "jobInforDetail": "Job milestone detail", "jobCodeLabel": "Job milestone code", "jobCodePlaceholder": "Enter job milestone code", "jobNameLabel": "Job milestone name", "jobNamePlaceholder": "Enter job milestone name", "jobNameLabelEng": "Job milestone name in English", "jobNamePlaceholderEng": "Enter job milestone name in English", "jobStatusLabel": "Status", "jobStatusUsing": "Using", "jobStatusNotUsing": "Not using", "jobSave": "Save", "jobDeleteConfirm": "Are you sure you want to delete this job milestone?", "jobCreateSuccess": "Create job milestone successfully", "jobUpdateSuccess": "Update job milestone successfully", "jobDeleteMessage": "Cannot delete job milestone that is being used", "jobCreateFailed": "Create job milestone failed", "jobUpdateFailed": "Update job milestone failed", "jobCodeRequired": "Data in Job milestone code field is invalid", "jobNameRequired": "Data in Job milestone name field is invalid", "jobCodeExist": "Job milestone code already exists", "jobCodeLength": "Do not enter more than 50 characters", "jobNameLength": "Do not enter more than 500 characters", "jobNameEngLength": "Do not enter more than 500 characters"}, "pts": {"ptsList": "Standard PTS list", "standardTime": "Standard time", "actualTime": "Recorded time", "oldActualTime": "Old recorded time", "newActualTime": "New recorded time", "standardEndTime": "Standard end time", "actualEndTime": "Actual end time", "description": "Description", "note": "Note", "status": "Status", "categoryLate": "Delayed job milestone", "completed": "Completed job milestone", "late": "Late", "onTime": "On time", "edit": "Edit", "jobGroup": "Job group", "newTimeLabel": "Enter new time", "code": "PTS code", "name": "PTS name", "time": "Flight time", "groundTime": "Ground time", "fromDate": "Effective from", "toDate": "Effective to", "aircraftType": "Aircraft type", "airport": "Airport", "minute": "minutes", "using": "Using", "notUsing": "Not using", "action": "Action", "network": "Flight network", "ptsCreate": "Create new standard PTS", "searchPlaceholder": "Search standard PTS", "fromTime": "Flight time from", "toTime": "Flight time to", "noMasterPTS": "No standard PTS has been added yet", "createFirstMasterPTS": "Create the first Master PTS to start managing the PTS standard set", "noPTS": "No PTS suitable for this flight", "addPTS": "Add PTS for flight", "noResult": "No results found", "recordSuccess": "Record successfully", "newTimeMessage": "Please enter new time", "record": "Record", "suitablePTSList": "List of suitable standard PTS", "selectPTSMessage": "Please select at least one standard PTS", "updatePtsSuccess": "Update PTS successfully", "selectPTS1Message": "Please select at least one standard PTS", "ptsAirport": "Standard PTS type for airport", "managementResponsibility": "Management unit", "resetPTS": "Reset flight PTS", "resetPTSMessage": "Are you sure you want to reset PTS for this flight? All applied job milestones will be deleted.", "confirmMessage": "Are you sure you want to duplicate this standard PTS?", "confirmTitle": "Confirm duplicate standard PTS", "createSuccess": "Create standard PTS successfully", "copySuccess": "Duplicate standard PTS successfully", "updateSuccess": "Update standard PTS successfully", "resetSuccess": "Reset PTS successfully", "copyMasterPTS": "Duplicate standard PTS", "copyMasterPTSMessage": "Are you sure you want to duplicate this standard PTS?", "resetPTSFlightSuccess": "Reset PTS for flight successfully", "ganttChart": "Gantt chart"}, "departure": {"late": "Late", "cancel": "Cancel", "inconstant": "Irregular", "tech_erorr": "Technical error", "vip_cip": "VIP/CIP", "refresh": "Refresh", "sync": "Sync", "searchPlaceholder": "Search flight", "listFlight": "Flight list", "flightInfo": "Flight information", "depApSched": "Departure airport", "arrApSched": "Arrival airport", "network": "Flight network", "fleetType": "Aircraft type", "legState": "Flight status", "notFoundFlightInfo": "No data found", "assignPTSTime": "Assignment time", "ptsCode": "PTS Code", "reasonLate": "Reason for delay", "reasonLateGT": "Reason for late GT", "unit": "Unit", "syncSuccess": "Sync successfully", "rangeDate": "Select date range"}, "ptsDetail": {"createdBy": "Created by", "used": "Used", "code": "PTS code", "codeMessage": "Enter PTS code", "codePlaceholder": "Enter PTS code", "name": "PTS name", "namePlaceholder": "Enter PTS name", "nameMessage": "Enter PTS name", "nameEng": "PTS name (English)", "nameEngPlaceholder": "Enter PTS name (English)", "nameEngMessage": "Enter PTS name (English)", "aircraftType": "Aircraft type", "aircraftTypePlaceholder": "Select aircraft type", "aircraftTypeMessage": "Select aircraft type", "airport": "Airport", "airportMessage": "Select airport", "airportPlaceholder": "Select airport", "flightNetwork": "International/Domestic flight network", "flightNetworkMessage": "Select international/domestic flight network", "flightNetworkPlaceholder": "Select international/domestic flight network", "flightType": "Flight classification", "groundTime": "Ground time (minutes)", "groundTimePlaceholder": "Enter ground time", "groundTimeMessage": "Enter ground time", "effectiveHourFrom": "Flight time from hour (Block Hour)", "effectiveHourFromPlaceholder": "Enter hours", "effectiveHourFromMessage": "Select flight time from", "effectiveHourTo": "Flight time to hour (Block Hour)", "effectiveHourToPlaceholder": "Enter hours", "effectiveHourToMessage": "Select flight time to", "effectiveDateFrom": "Effective from date", "effectiveDateFromPlaceholder": "Enter effective date", "effectiveDateFromMessage": "Select effective time from", "effectiveDateToPlaceholder": "Enter effective date", "effectiveDateTo": "Effective to date", "effectiveDateToMessage": "Select effective time to", "listPTS": "PTS job list", "sortAscending": "In ascending order", "sortDescending": "In descending order", "createPTS": "Add PTS job milestone", "noPTS": "No PTS job milestone has been added yet", "pts": "PTS job milestone", "jobCode_Name": "PTS job milestone code - name", "jobCodeMessage": "Select job milestone", "jobName": "Job milestone name", "jobNamePlaceholder": "Auto-fill job milestone code", "completeTime": "Standard time", "completeTimePlaceholder": "minutes", "managementResponsibility": "Management responsibility", "jobCode_NamePlaceholder": "Enter job milestone code or name", "managementResponsibilityPlaceholder": "Select unit", "standardPTS": "Standard PTS", "detailInfo": "Detailed information", "jobGroup": "Job milestone group", "jobGroupPlaceholder": "Enter or select job milestone group name", "completeTimeMessage": "Enter standard completion time", "completeTimeMessage2": "Standard completion time must not be greater than 0", "managementResponsibilityMessage": "Select management responsibility", "createPTSMessage": "Please add at least one PTS job milestone", "fromDateMustBeBeforeToDate": "Effective time from must be before effective time to", "toDateMustBeAfterFromDate": "Effective time to must be after effective time from", "codeLengthMessage": "PTS code maximum 50 characters", "nameLengthMessage": "PTS name maximum 200 characters", "completeTimeMessage3": "Standard completion time of job milestone must not be greater than GT time of PTS", "jobCode": "Job milestone code", "sortIndex": "By input order", "executeOUIds": "Executing unit", "executeOUIdsPlaceholder": "Select executing unit"}, "login": {"username": "Email/Username", "password": "Password", "rememberMe": "Remember me", "login": "<PERSON><PERSON>", "forgotPassword": "Forgot password", "loginSuccess": "Login successful", "loginFailed": "<PERSON><PERSON> failed", "emailPlaceholder": "Enter email", "passwordPlaceholder": "Enter password", "passwordRequired": "Password is required", "loginProvider": "Login method", "loginLdap": "Login with Active Directory", "loginBasic": "Login with Pas<PERSON>", "AD": "ACTIVE DIRECTORY"}, "user": {"userList": "User list", "userCreate": "Create new user", "userEdit": "Edit user", "userDelete": "Delete user", "changePassword": "Change password", "confirmDelete": "Are you sure you want to delete this user?", "userInfo": "User information", "roles": "Roles", "userName": "Username", "name": "Full name", "password": "Password", "email": "Email", "phoneNumber": "Phone number", "active": "Active", "accountLockout": "Account lockout", "emailConfirmed": "Email confirmed", "phoneNumberConfirmed": "Phone number confirmed", "accessFailedCount": "Failed login attempts", "creationTime": "Creation time", "lastModificationTime": "Last modification time", "twoFactorEnabled": "Two-factor enabled", "action": "Action", "accountLockoutTooltip": "Lock account after multiple failed login attempts", "userNameRequired": "Username is required", "passwordRequired": "Password is required", "emailRequired": "Email is required", "emailInvalid": "Invalid email", "filter": {"role": "Role", "organizationUnit": "Organization unit", "userName": "Username", "name": "Full name", "creationDate": "Creation date", "modificationDate": "Modification date", "phoneNumber": "Phone number", "email": "Email", "notActive": "Not active", "emailConfirmed": "Email confirmed", "lock": "Lock", "externalUser": "External user"}, "passwordPlaceholder": "Enter password", "userCreateSuccess": "Create user successfully", "userUpdateSuccess": "Update user successfully", "userDeleteSuccess": "Delete user successfully", "userCreateFail": "Create user failed", "userUpdateFail": "Update user failed", "userDeleteFail": "Delete user failed", "searchPlaceholder": "Search user", "phoneNumberInvalid": "Invalid phone number. Please enter again."}, "role": {"roleName": "Role name", "isDefault": "<PERSON><PERSON><PERSON>", "isPublic": "Public", "action": "Action", "userCount": "User count", "add": "Create new role", "updateRole": "Update role", "newRole": "Create new role", "permissions": "Permissions", "roleList": "Role list", "permission": "Permissions for", "selectAll": "Select all", "createRoleSuccess": "Create role successfully", "updateRoleSuccess": "Update role successfully", "deleteRoleSuccess": "Delete role successfully"}, "organization": {"name": "Unit name", "code": "Unit code", "management": "Organization management", "info": "Unit information", "organizationTree": {"_": "Organization tree", "add": "Add new unit", "addSub": "Add sub-unit", "edit": "Edit", "delete": "Delete"}, "member": {"name": "User", "email": "Email", "role": "Permission", "selectRole": "Select permission", "roleEdit": "Edit permission", "add": "Add member", "createSuccess": "Add member successfully", "createFail": "Add member failed", "delete": "Delete member from department", "deleteSuccess": "Delete member successfully", "deleteFail": "Delete member failed"}, "nameRequired": "Please enter unit name", "codeRequired": "Please enter unit code"}, "airport": {"iataCode": "IATA Code", "icaoCode": "ICAO Code", "apName": "Airport name", "countryCode": "Airport country code", "create": "Create new airport", "update": "Update airport", "delete": "Delete airport", "deleteConfirm": "Are you sure you want to delete this airport?", "deleteSuccess": "Delete airport successfully", "deleteFail": "Delete airport failed", "createFail": "Create airport failed", "updateFail": "Update airport failed", "iataCodeRequired": "IATA Code is required", "icaoCodeRequired": "ICAO Code is required", "apNameRequired": "Airport name is required", "countryCodeRequired": "Country code is required", "updateSuccess": "Update successfully", "createSuccess": "Create successfully", "searchPlaceholder": "Search airport", "employee_list": "Employee list", "STT": "No.", "employee": "Employee", "unit": "Unit", "add_employee": "Add employee", "search_by_employee_name": "Enter employee name", "add_employee_success": "Add employee successfully", "add_employee_fail": "Add employee failed", "remove_employee": "Remove employee", "remove_employee_confirm": "Are you sure you want to remove this employee?", "remove_employee_success": "Remove employee successfully", "remove_employee_fail": "Remove employee failed", "airport_list": "Airport list", "email": "Email", "searchUserPlaceholder": "Search employee"}, "groupTaskPTS": {"code": "PTS job milestone group code", "name": "PTS job milestone group name", "create": "Create new PTS job milestone group", "update": "Update PTS job milestone group", "delete": "Delete PTS job milestone group", "createdDate": "Created date", "action": "Action", "description": "Description", "createSuccess": "Create PTS job milestone group successfully", "updateSuccess": "Update PTS job milestone group successfully", "deleteSuccess": "Delete PTS job milestone group successfully", "add": "Create new job milestone group", "edit": "Edit PTS job milestone group", "list": "PTS job milestone group list", "codeRequired": "PTS job milestone group code is required", "nameRequired": "PTS job milestone group name is required", "descriptionRequired": "Description is required"}, "fleet": {"code": "Aircraft code", "description": "Description", "create": "Create new aircraft", "update": "Update aircraft", "delete": "Delete aircraft", "createdDate": "Created date", "action": "Action", "createSuccess": "Create aircraft successfully", "updateSuccess": "Update aircraft successfully", "deleteSuccess": "Delete aircraft successfully", "add": "Create new aircraft", "edit": "Edit aircraft", "searchPlaceholder": "Search aircraft", "list": "Aircraft list", "codeRequired": "Aircraft code is required", "descriptionRequired": "Description is required"}, "dashboard": {"index": "No.", "title": "Dashboard", "last7Days": "Last 7 days", "last14Days": "Last 14 days", "last30Days": "Last 30 days", "last90Days": "Last 90 days", "lateGT": "Late Ground Time", "onTimeGT": "On-time Ground Time", "output": "Output", "departure": "Departure flight", "unitLate": "Delayed unit", "groupPtsTask": "PTS task group", "groupPtsTaskLate": "PTS task group overtime", "task": "Task", "taskLate": "Overtime task", "departureNotMetOSP": "Departure flight not meeting OSP", "departureOTPCompliant": "Departure flight meeting OTP", "departureNotMetOTP": "Departure flight not meeting OTP", "count": "Count", "flight": "flights", "departureOSPCompliant": "Departure flight meeting OSP", "listDepartureNotMetOSP": "List of departure flights not meeting OSP", "listDepartureNotMetOTP": "List of departure flights not meeting OTP", "otpDelayUnit": "Unit causing PTS delay - OTP", "osDelayUnit": "Unit causing PTS delay - OSP", "gtExceeded": "Flights exceeding standard GT", "gtStandard": "Flights with standard GT", "download": "Download", "view_detail": "View details", "department": "Unit", "totalDelay": "Total overtime duration", "listUnitLate": "List of units causing delayed PTS group - OTP", "unitSearchPlaceholder": "Search unit", "unit": "units", "allAirport": "All airports", "refreshDataAt": "Data last updated at", "ratioOTP": "Ratio OTP", "ratioOSP": "Ratio OSP", "lastestUpdated": "Lastest updated at", "noPtsRecorded": "No PTS recorded", "lateOTP": "Late OTP", "lateOSP": "Late OSP"}, "report": {"searchFlightPlaceholder": "Search flight", "download": "Download", "reportGroupTask": "Delayed task group", "of": "of", "reportDepartment": "Units serving overtime", "reportFlight": "Delayed flights", "flightPlaceholder": "Select flight", "departmentPlaceholder": "Select unit", "allAirportPlaceholder": "All airports", "allDepartmentPlaceholder": "All units", "allGroupTaskPlaceholder": "All PTS task groups", "allGroupTask": "All PTS task groups", "reportOnTimeGT": "Flights serving on-time GT", "searchFlightNoPlaceholder": "Search flight", "searchAirportPlaceholder": "Search airport", "reportLateGT": "Flights serving late GT", "reportPTSTaskLate": "PTS task serving late", "reportNoRecordPTS": "Flights not recording PTS", "allPtsGroupTaskPlaceholder": "All PTS task groups", "reportGroupTaskLate": "Delayed task group", "reportDepartmentLate": "Units causing delayed task group", "reportFlightOSP": "Flights not meeting OSP", "reportFlightOTP": "Flights not meeting OTP", "allPtsTaskPlaceholder": "All PTS tasks"}, "modalFlight": {"choosenFile": "Choose file", "noFile": "No file chosen", "uploadLimit": "Only upload file size less than 20MB"}, "button": {"approve": "Approve", "revoke": "Revoke"}}