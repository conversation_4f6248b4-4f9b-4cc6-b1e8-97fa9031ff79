/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { DISPLAY_DATE, ISO_DATETIME } from '@/src/constants'
import { disabledDaysDate } from '@/src/helper/disabledDayDate'
import { handleApiError } from '@/src/helper/handleApiError'
import { renderTimeWithSign } from '@/src/helper/renderTimeSign'
import { isValidRangeDate } from '@/src/helper/validDate'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import useDebounce from '@/src/hooks/useDebounce'
import type { IAirport } from '@/src/schema/IAirport'
import { getAirport } from '@/src/service/airport'
import { getFleet } from '@/src/service/fleet'
import { getFlight } from '@/src/service/flight'
import { getLegState } from '@/src/service/leg_state'
import { getNature } from '@/src/service/nature'
import { getNetwork } from '@/src/service/network'
import { syncFlight } from '@/src/service/sync'
import {
  closeDropdownViewColumn,
  closeFilterModal,
  openDropdownViewColumn,
  openFilterModal,
  setCheckListFlight,
  setParams,
  setSelectedFlightId,
  setSelectedPTSMasterId,
} from '@/src/store/ArrivalSlice'
import {
  openModalArrivalFlightDetail,
  setSelectedFlightModalId,
} from '@/src/store/ModalArrivalFlightSlice'
import {
  FilterFilled,
  ReloadOutlined,
  SearchOutlined,
  SyncOutlined,
} from '@ant-design/icons'
import { useMutation, useQuery } from '@tanstack/react-query'
import type {
  InputRef,
  TableColumnsType,
  TableColumnType,
  TableProps,
} from 'antd'
import {
  Button,
  DatePicker,
  Flex,
  Form,
  Input,
  message,
  Popover,
  Radio,
  Select,
  Space,
  Table,
  TimePicker,
} from 'antd'
import type { FilterDropdownProps } from 'antd/es/table/interface'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import QueryString from 'qs'
import { lazy, Suspense, useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useLocation, useNavigate, useSearchParams } from 'react-router'
import DropdownChangeColumn from '../../components/DropdownColumn'
import styles from './index.module.scss'
const ModalArrivalFlightDetail = lazy(
  () => import('@/src/pages/Arrival/components/ModalArrivalFlightDetail')
)
dayjs.extend(utc)
const { RangePicker } = DatePicker

type OnChange = NonNullable<TableProps<any>['onChange']>
type Filters = Parameters<OnChange>[1]

const ArrivalPage = () => {
  const { t } = useTranslation()

  const { airportGlobal, settings } = useAppSelector(state => state.global)

  const [filteredInfo, setFilteredInfo] = useState<Filters>({})

  const navigate = useNavigate()

  const [form] = Form.useForm()

  const [formHour] = Form.useForm()

  const [searchParams] = useSearchParams()
  const paramLocation = useLocation().search

  const [keyword, setKeyword] = useState<undefined | string>(undefined)
  const debouncedKeyword = useDebounce(keyword, 500)

  const [totalCount, setTotalCount] = useState<number>(0)

  const now = dayjs().startOf('hour')
  const start = now.add(settings?.startHour || -1, 'hour')
  const end = now.add(settings?.endHour || 3, 'hour')

  const [paramsArrival, setParamsArrival] = useState<{
    maxResultCount: number
    skipCount: number
    fromDate: string
    toDate: string
    fleetTypes: string[]
    legStates: string[]
    networks: string[]
    depApSched: string
    arrApSched: string
    keyWord: string
    fnCarrier: string
    fromHour: string
    toHour: string
  }>(() => {
    const queryParams = QueryString.parse(paramLocation, {
      ignoreQueryPrefix: true,
    })

    const from =
      typeof queryParams.fromDate === 'string'
        ? dayjs(queryParams.fromDate)
        : null

    const to =
      typeof queryParams.toDate === 'string' ? dayjs(queryParams.toDate) : null

    const valid = isValidRangeDate(from, to)

    const value: any = {
      ...queryParams,
      skipCount: Number(queryParams.skipCount) || 0,
      maxResultCount: Number(queryParams.maxResultCount) || 1000,
      fromDate:
        valid && from
          ? from?.format(ISO_DATETIME)
          : dayjs().startOf('day').format(ISO_DATETIME),
      toDate:
        valid && to
          ? to?.format(ISO_DATETIME)
          : dayjs().endOf('day').format(ISO_DATETIME),
      fleetTypes: queryParams.fleetTypes || [],
      legStates: queryParams.legStates || [],
      networks: queryParams.networks || [],
      depApSched: queryParams.depApSched || '',
      arrApSched: airportGlobal,
      fnCarrier: queryParams.fnCarrier || '',
    }

    return value
  })

  const {
    selectedFlightId,
    visibleFilterModal,
    visibleDropdownViewColumn,
    checkListFlight,
  } = useAppSelector(state => state.arrival)

  const { params: airportParams } = useAppSelector(state => state.airport)

  const query = useQuery({
    queryKey: ['arrival', paramsArrival, airportGlobal],
    queryFn: () =>
      getFlight({
        ...paramsArrival,
        arrApSched: airportGlobal,
      }),
    enabled: !!airportGlobal,
    refetchInterval: 3 * 60 * 1000,
  })

  const { data: airportData } = useQuery({
    queryKey: ['get-airport-list', airportParams],
    queryFn: () =>
      getAirport({
        MaxResultCount: 1000,
      }),
    enabled: !!visibleFilterModal,
  })

  const { data: networkData } = useQuery({
    queryKey: ['get-network-list'],
    queryFn: () => getNetwork(),
    enabled: !!visibleFilterModal,
  })

  const { data: legStateData } = useQuery({
    queryKey: ['get-leg-state-list'],
    queryFn: () => getLegState(),
    enabled: !!visibleFilterModal,
  })

  const { data: fleetData } = useQuery({
    queryKey: ['get-fleet-list'],
    queryFn: () =>
      getFleet({
        MaxResultCount: 1000,
      }),
    enabled: !!visibleFilterModal,
  })

  const { data: natureData } = useQuery({
    queryKey: ['get-nature-list'],
    queryFn: () => getNature(),
  })

  const mutationSync = useMutation({
    mutationFn: async () => {
      return syncFlight()
    },
    onSuccess: () => {
      message.success(t('departure.syncSuccess'))
    },
    onError: (error: any) => {
      handleApiError(error)
    },
  })

  const dispatch = useAppDispatch()

  const searchInput = useRef<InputRef>(null)

  const handleSearch = (
    _selectedKeys: string[],
    confirm: FilterDropdownProps['confirm'],
    _dataIndex: any
  ) => {
    confirm()
  }

  const handleReset = (
    clearFilters: () => void,
    confirm: FilterDropdownProps['confirm']
  ) => {
    clearFilters()
    confirm()
  }

  type SearchOptions = {
    placeholder?: string
    customFilter?: (value: string, record: any) => boolean
  }

  const getColumnSearchProps = (
    dataIndex: string,
    options?: SearchOptions
  ): TableColumnType<any> => ({
    filterDropdown: ({
      setSelectedKeys,
      selectedKeys,
      confirm,
      clearFilters,
      close,
    }) => (
      <div className="p-2 flex flex-col" onKeyDown={e => e.stopPropagation()}>
        <Input
          ref={searchInput}
          placeholder={options?.placeholder || `Search ${dataIndex}`}
          value={selectedKeys[0]}
          onChange={e => {
            setSelectedKeys(e.target.value ? [e.target.value] : [])
          }}
          onPressEnter={() => {
            handleSearch(selectedKeys as string[], confirm, dataIndex)
          }}
          className="!mb-2 block !w-full"
        />
        <Space>
          <Button
            type="primary"
            onClick={() => {
              handleSearch(selectedKeys as string[], confirm, dataIndex)
            }}
            icon={<SearchOutlined />}
            size="small"
            style={{ width: 90 }}
          >
            Search
          </Button>
          <Button
            onClick={() => clearFilters && handleReset(clearFilters, confirm)}
            size="small"
            style={{ width: 90 }}
          >
            Reset
          </Button>
          <Button type="link" size="small" onClick={() => close()}>
            Close
          </Button>
        </Space>
      </div>
    ),
    filterIcon: (filtered: boolean) => (
      <SearchOutlined style={{ color: filtered ? '#006885' : undefined }} />
    ),
    onFilter: (value, record) => {
      const searchValue = (value as string).toLowerCase()

      // custom filter được ưu tiên
      if (options?.customFilter) {
        return options.customFilter(searchValue, record)
      }

      // default
      return record[dataIndex]
        ? record[dataIndex].toString().toLowerCase().includes(searchValue)
        : false
    },
    filterDropdownProps: {
      onOpenChange(open) {
        if (open) {
          setTimeout(() => searchInput.current?.select(), 100)
        }
      },
    },
  })

  const columns: TableColumnsType<any> = [
    {
      key: 'index',
      title: t('table.order'),
      align: 'center',
      render: (_, __, index) => paramsArrival.skipCount + index + 1,
    },
    {
      key: 'registerNo',
      dataIndex: 'acRegistration',
      title: 'Register No',
      hidden: false,
      align: 'center',
      filteredValue: filteredInfo.registerNo || null,
      ...getColumnSearchProps('acRegistration', {
        placeholder: 'Search Register No',
      }),
    },
    {
      key: 'aircraft',
      dataIndex: 'acSubType',
      title: 'Aircraft',
      hidden: false,
      align: 'center',
      filteredValue: filteredInfo.aircraft || null,
      ...getColumnSearchProps('acSubType', {
        placeholder: 'Search Aircraft',
      }),
    },
    {
      key: 'nature',
      dataIndex: 'fltType',
      title: 'Nature',
      hidden: false,
      align: 'center',
      filters: natureData?.map((item: any) => ({
        text: item.name,
        value: item.name,
      })),
      filteredValue: filteredInfo.nature || null,
      onFilter: (value, record) => record.fltType.includes(value as string),
      ellipsis: true,
      filterSearch: true,
    },
    {
      key: 'flightNo',
      title: 'Flight No',
      hidden: false,
      render: (_value, record) => record.fnCarrier + record.fnNumber,
      align: 'center',
      filteredValue: filteredInfo.flightNo || null,
      ...getColumnSearchProps('flightNo', {
        placeholder: 'Search Flight No',
        customFilter: (value, record) => {
          const flightNo = record.fnCarrier + record.fnNumber
          return flightNo ? flightNo.toLowerCase().includes(value) : false
        },
      }),
    },
    {
      key: 'codeShare',
      dataIndex: 'codeShare',
      title: 'Code share',
      hidden: false,
      align: 'center',
      filteredValue: filteredInfo.codeShare || null,
      ...getColumnSearchProps('codeShare', {
        placeholder: 'Search Code share',
      }),
    },
    {
      key: 'routing',
      title: 'Routing',
      hidden: false,
      render: (_value, record) => `${record.depApSched}-${record.arrApSched}`,
      align: 'center',
      filteredValue: filteredInfo.routing || null,
      ...getColumnSearchProps('routing', {
        placeholder: 'Search Routing',
        customFilter: (value, record) => {
          const routing = `${record.depApSched}-${record.arrApSched}`
          return routing ? routing.toLowerCase().includes(value) : false
        },
      }),
    },
    {
      key: 'sta',
      dataIndex: 'depSchedDt',
      title: 'STA',
      hidden: false,
      align: 'center',
      render: (value, record) => (
        <>{renderTimeWithSign(value, record.depSchedDt)}</>
      ),
    },
    {
      key: 'eta',
      dataIndex: 'depDt',
      title: 'ETA',
      hidden: false,
      align: 'center',
      render: (value, record) => (
        <>{renderTimeWithSign(value, record.depSchedDt)}</>
      ),
    },
    // {
    //   key: 'tda',
    //   dataIndex: 'depDt',
    //   title: 'TDA',
    //   hidden: false,
    //   align: 'center',
    //   render: value => <>{moment(value).format(ISO_DATETIME_NOSECOND)}</>,
    //   width: 150,
    // },
    {
      key: 'ata',
      dataIndex: 'depDt',
      title: 'ATA',
      hidden: false,
      align: 'center',
      render: (value, record) => (
        <>{renderTimeWithSign(value, record.depSchedDt)}</>
      ),
    },
    {
      key: 'bay',
      dataIndex: 'bay',
      title: 'Bay',
      hidden: false,
      align: 'center',
      filteredValue: filteredInfo.bay || null,
      ...getColumnSearchProps('bay', {
        placeholder: 'Search Bay',
      }),
    },
    {
      key: 'belt',
      dataIndex: 'bay',
      title: 'Belt',
      hidden: false,
      align: 'center',
      filteredValue: filteredInfo.belt || null,
      ...getColumnSearchProps('belt', {
        placeholder: 'Search Belt',
      }),
    },
    {
      key: 'nextFlight',
      title: 'Next Flight',
      hidden: false,
      align: 'center',
      render: (_value, record) => record.fnCarrier + record.fnNumber,
      filteredValue: filteredInfo.nextFlight || null,
      ...getColumnSearchProps('nextFlight', {
        placeholder: 'Search Next Flight',
        customFilter: (value, record) => {
          const nextFlight = record.fnCarrier + record.fnNumber
          return nextFlight ? nextFlight.toLowerCase().includes(value) : false
        },
      }),
    },
    {
      key: 'status',
      dataIndex: 'legState',
      title: 'Status',
      hidden: false,
      align: 'center',
      filters: legStateData?.map((item: any) => ({
        text: item.name,
        value: item.name,
      })),
      filteredValue: filteredInfo.status || null,
      onFilter: (value, record) => record.legState.includes(value as string),
      ellipsis: true,
      filterSearch: true,
    },
    // {
    //   key: 'sabreCAPC',
    //   title: '1A CAPC',
    //   hidden: false,
    //   width: 100,
    //   align: 'center',
    // },
    // {
    //   key: 'booking',
    //   title: 'Booking',
    //   hidden: false,
    //   width: 100,
    //   align: 'center',
    // },
    // {
    //   key: 'onboardPax',
    //   dataIndex: 'ckiC',
    //   title: 'Onboard Pax',
    //   hidden: false,
    //   width: 100,
    //   align: 'center',
    // },
    {
      key: 'vip',
      dataIndex: 'vip',
      title: 'VIP',
      hidden: false,
      align: 'center',
      onCell: (record: any) => ({
        className: record.vip && record.vip1 ? 'bg-[#FB4E4E]' : '',
      }),
    },
    {
      key: 'remark',
      dataIndex: 'remark',
      title: 'Remark',
      hidden: false,
      width: 300,
      filteredValue: filteredInfo.remark || null,
      ...getColumnSearchProps('remark', {
        placeholder: 'Search Remark',
      }),
    },
    {
      key: 'eval',
      dataIndex: 'eval',
      title: 'Eval',
      hidden: false,
      align: 'center',
      width: 300,
      filteredValue: filteredInfo.eval || null,
      ...getColumnSearchProps('eval', {
        placeholder: 'Search Eval',
      }),
    },
    {
      key: 'onDuties',
      dataIndex: 'duties',
      title: 'On duties',
      hidden: false,
      align: 'center',
      width: 300,
      filteredValue: filteredInfo.onDuties || null,
      ...getColumnSearchProps('duties', {
        placeholder: 'Search On duties',
      }),
    },
  ]

  const newColumns = columns.map(item => ({
    ...item,
    hidden: !checkListFlight.includes(item.key as string),
  }))

  const handleChange: OnChange = (_, filters, __, extra) => {
    setTotalCount(extra.currentDataSource.length)
    setFilteredInfo(filters)
  }

  const renderTitleTable = () => {
    return (
      <div className="w-full flex justify-end">
        <div className="flex flex-row gap-x-2 mt-2">
          <Button
            type="primary"
            icon={<ReloadOutlined />}
            className="!text-sm !font-bold"
            onClick={() => query.refetch()}
            loading={query.isRefetching}
          >
            {t('departure.refresh')}
          </Button>
          <Button
            type="primary"
            icon={<SyncOutlined />}
            className="!text-sm !font-bold"
            loading={mutationSync.isPending}
            disabled={mutationSync.isPending}
            onClick={() => mutationSync.mutate()}
          >
            {t('departure.sync')}
          </Button>
          <Form
            form={formHour}
            layout="inline"
            initialValues={{
              rangeHour: [start, end],
            }}
          >
            <Form.Item name="rangeHour">
              <TimePicker.RangePicker
                format="HH:00"
                className="!w-42"
                allowClear={false}
                onChange={(values: any) => {
                  if (values) {
                    const [start, end] = values
                    setParamsArrival({
                      ...paramsArrival,
                      skipCount: 0,
                      fromHour: start.format('HH:00'),
                      toHour: end.format('HH:00'),
                    })
                  }
                }}
              />
            </Form.Item>
          </Form>
          <Input
            prefix={<SearchOutlined />}
            placeholder={t('departure.searchPlaceholder')}
            className="!w-[200px]"
            defaultValue={searchParams.get('keyWord') || ''}
            onChange={e => {
              setKeyword(e.target.value)
            }}
          />
          <Popover
            open={visibleFilterModal}
            arrow={false}
            placement="bottomRight"
            trigger={['click']}
            content={
              <Form
                className="w-[500px]"
                form={form}
                layout="vertical"
                initialValues={{
                  rangeDate: [
                    dayjs(paramsArrival.fromDate),
                    dayjs(paramsArrival.toDate),
                  ],
                  depApSched: paramsArrival.depApSched,
                  arrApSched: airportGlobal,
                  network: paramsArrival.networks,
                  fleetType: paramsArrival.fleetTypes,
                  legState: paramsArrival.legStates,
                }}
              >
                <Form.Item name="rangeDate" label={t('departure.rangeDate')}>
                  <RangePicker
                    allowClear={false}
                    defaultValue={[
                      dayjs(paramsArrival.fromDate),
                      dayjs(paramsArrival.toDate),
                    ]}
                    format={DISPLAY_DATE}
                    className="w-full"
                    disabledDate={disabledDaysDate(7)}
                  />
                </Form.Item>
                <Flex className="flex flex-row w-full gap-x-6">
                  <Form.Item
                    name="depApSched"
                    label={t('departure.depApSched')}
                    className="w-1/2"
                  >
                    <Select
                      allowClear
                      showSearch
                      filterOption={(input, option) =>
                        (option?.label ?? '')
                          .toLowerCase()
                          .includes(input.toLowerCase())
                      }
                      placeholder={t('common.all')}
                      options={[
                        { value: '', label: `${t('common.all')}` },
                        ...(airportData?.items.map((item: IAirport) => ({
                          value: item.iataCode,
                          label: item.iataCode,
                        })) || []),
                      ]}
                    />
                  </Form.Item>
                  <Form.Item
                    name="arrApSched"
                    label={t('departure.arrApSched')}
                    className="w-1/2"
                  >
                    <Select
                      allowClear
                      showSearch
                      className="pointer-events-none"
                      filterOption={(input, option) =>
                        (option?.label ?? '')
                          .toLowerCase()
                          .includes(input.toLowerCase())
                      }
                      value={airportGlobal}
                      placeholder={t('common.all')}
                      options={[
                        { value: null, label: `${t('common.all')}` },
                        ...(airportData?.items.map((item: IAirport) => ({
                          value: item.iataCode,
                          label: item.iataCode,
                        })) || []),
                      ]}
                    />
                  </Form.Item>
                </Flex>
                <Flex className="flex flex-row w-full gap-x-6">
                  <Form.Item
                    name="network"
                    label={t('departure.network')}
                    className="w-1/2"
                  >
                    <Select
                      mode="multiple"
                      allowClear
                      showSearch
                      filterOption={(input, option) =>
                        (option?.label ?? '')
                          .toLowerCase()
                          .includes(input.toLowerCase())
                      }
                      defaultValue={paramsArrival.networks}
                      placeholder={t('common.all')}
                      options={[
                        ...(networkData?.map((item: any) => ({
                          value: item.name,
                          label: item.name,
                        })) || []),
                      ]}
                    />
                  </Form.Item>
                  <Form.Item
                    name="fleetType"
                    label={t('departure.fleetType')}
                    className="w-1/2"
                  >
                    <Select
                      mode="multiple"
                      allowClear
                      showSearch
                      filterOption={(input, option) =>
                        (option?.label ?? '')
                          .toLowerCase()
                          .includes(input.toLowerCase())
                      }
                      placeholder={t('common.all')}
                      options={[
                        ...(fleetData?.items.map((item: any) => ({
                          value: item.code,
                          label: item.code,
                        })) || []),
                      ]}
                    />
                  </Form.Item>
                </Flex>
                <Form.Item
                  name="legState"
                  label={t('departure.legState')}
                  className="w-1/2 !pr-3"
                >
                  <Select
                    className="w-full"
                    mode="multiple"
                    allowClear
                    showSearch
                    filterOption={(input, option) =>
                      (option?.label ?? '')
                        .toLowerCase()
                        .includes(input.toLowerCase())
                    }
                    placeholder={t('common.all')}
                    options={[
                      ...(legStateData?.map((item: any) => ({
                        value: item.name,
                        label: item.name,
                      })) || []),
                    ]}
                  />
                </Form.Item>
                <Form.Item name="fnCarrier" label="">
                  <Radio.Group defaultValue={''}>
                    <Radio value="">All</Radio>
                    <Radio value="ov">OV</Radio>
                    <Radio value="bl">BL</Radio>
                    <Radio value="vn">VN</Radio>
                  </Radio.Group>
                </Form.Item>
                <div className="flex justify-end gap-x-2">
                  <Button
                    onClick={() => {
                      dispatch(closeFilterModal())
                    }}
                  >
                    {t('common.cancel')}
                  </Button>

                  <Button
                    onClick={() => {
                      const defaultValues = {
                        rangeDate: [
                          dayjs().startOf('day'),
                          dayjs().endOf('day'),
                        ],
                        depApSched: '',
                        arrApSched: airportGlobal,
                        network: [],
                        fleetType: [],
                        legState: [],
                        fnCarrier: '',
                      }

                      form.resetFields()
                      form.setFieldsValue(defaultValues)

                      setParamsArrival({
                        ...paramsArrival,
                        fromDate: dayjs().startOf('day').format(ISO_DATETIME),
                        toDate: dayjs().endOf('day').format(ISO_DATETIME),
                        depApSched: '',
                        arrApSched: airportGlobal,
                        networks: [],
                        fleetTypes: [],
                        legStates: [],
                        fnCarrier: '',
                        skipCount: 0,
                      })
                    }}
                  >
                    {t('common.reset')}
                  </Button>

                  <Button
                    type="primary"
                    onClick={() => {
                      const rangeDate = form.getFieldValue('rangeDate') || [
                        dayjs().startOf('day'),
                        dayjs().endOf('day'),
                      ]

                      const fromDate = dayjs(rangeDate[0])
                      const toDate = dayjs(rangeDate[1])

                      let fromHour = paramsArrival.fromHour || '00:00'
                      let toHour = paramsArrival.toHour || '00:00'

                      if (!fromDate.isSame(toDate, 'day')) {
                        fromHour = '00:00'
                        toHour = '00:00'

                        formHour.setFieldsValue({
                          rangeHour: [
                            dayjs('00:00', 'HH:mm'),
                            dayjs('00:00', 'HH:mm'),
                          ],
                        })
                      }

                      const newParams = {
                        ...paramsArrival,
                        depApSched: form.getFieldValue('depApSched') || '',
                        arrApSched: airportGlobal,
                        skipCount: 0,
                        fromDate: fromDate.format(ISO_DATETIME),
                        toDate: toDate.format(ISO_DATETIME),
                        fromHour,
                        toHour,
                        networks: form.getFieldValue('network') || [],
                        fleetTypes: form.getFieldValue('fleetType') || [],
                        legStates: form.getFieldValue('legState') || [],
                        fnCarrier: form.getFieldValue('fnCarrier') || '',
                      }

                      setParamsArrival(newParams)
                      dispatch(closeFilterModal())
                    }}
                  >
                    {t('common.apply')}
                  </Button>
                </div>
              </Form>
            }
          >
            <Button
              icon={<FilterFilled />}
              onClick={() => {
                if (visibleFilterModal) {
                  dispatch(closeFilterModal())
                } else {
                  dispatch(openFilterModal())
                }
              }}
            >
              {t('common.filter')}
            </Button>
          </Popover>
          <DropdownChangeColumn
            columns={columns || []}
            onChangeColumn={val => dispatch(setCheckListFlight(val))}
            onOk={() => dispatch(openDropdownViewColumn())}
            onCancel={() => dispatch(closeDropdownViewColumn())}
            open={visibleDropdownViewColumn}
          />
        </div>
      </div>
    )
  }

  useEffect(() => {
    if (keyword !== undefined) {
      setParamsArrival({
        ...paramsArrival,
        skipCount: 0,
        keyWord: debouncedKeyword as string,
      })
    }
  }, [debouncedKeyword])

  useEffect(() => {
    dispatch(setCheckListFlight(columns.map(item => item.key as string)))
  }, [])

  useEffect(() => {
    navigate(
      `/flight-schedule/arrival?${QueryString.stringify(paramsArrival)}`,
      { replace: true }
    )
    dispatch(setParams(paramsArrival))
  }, [paramsArrival])

  useEffect(() => {
    setParamsArrival({
      ...paramsArrival,
      skipCount: 0,
    })
    dispatch(closeFilterModal())
    form.setFieldsValue({ arrApSched: airportGlobal })
  }, [airportGlobal])

  useEffect(() => {
    dispatch(setSelectedFlightId(''))
  }, [])

  return (
    <div className="flex flex-col gap-y-4">
      <Table
        columns={newColumns}
        bordered
        size="small"
        className={`${styles.whiteHeader}`}
        title={renderTitleTable}
        scroll={{ x: 'max-content' }}
        dataSource={query.data?.items || []}
        onRow={record => ({
          onClick: () => {
            dispatch(setSelectedFlightId(record.id))
            dispatch(setSelectedPTSMasterId(record.ptsId))
          },
          onDoubleClick: () => {
            dispatch(openModalArrivalFlightDetail())
            dispatch(setSelectedFlightModalId(record.id))
          },
        })}
        rowHoverable={false}
        rowClassName={record => {
          return record.id === selectedFlightId ? '!bg-[#E6F0F3]' : ''
        }}
        // pagination={{
        //   total: query.data?.totalCount || 0,
        //   current: paramsArrival.skipCount / paramsArrival.maxResultCount + 1,
        //   pageSize: paramsArrival.maxResultCount,
        //   onChange: (page, pageSize) => {
        //     setParamsArrival({
        //       ...paramsArrival,
        //       skipCount: (page - 1) * pageSize,
        //       maxResultCount: pageSize,
        //     })
        //   },
        //   showSizeChanger: true,
        //   showTotal: (total, range) => (
        //     <ShowTotal total={total} range={range} />
        //   ),
        // }}
        onChange={handleChange}
        pagination={false}
        footer={() => {
          const hasFilter = Object.values(filteredInfo || {}).some(
            v => v && v.length > 0
          )

          return (
            <div className="justify-end flex">
              {t('common.total')}:&nbsp;
              {hasFilter ? totalCount : query.data?.totalCount}&nbsp;
              {t('common.record')}
            </div>
          )
        }}
        rowKey={record => record.id}
        loading={query.isLoading}
      />
      <Suspense>
        <ModalArrivalFlightDetail />
      </Suspense>
    </div>
  )
}

export default ArrivalPage
