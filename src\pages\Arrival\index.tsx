/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import ShowTotal from '@/src/components/Showtotal'
import { DISPLAY_DATE, ISO_DATETIME } from '@/src/constants'
import { handleApiError } from '@/src/helper/handleApiError'
import { renderTimeWithSign } from '@/src/helper/renderTimeSign'
import { isValidRangeDate } from '@/src/helper/validDate'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import useDebounce from '@/src/hooks/useDebounce'
import type { IAirport } from '@/src/schema/IAirport'
import { getAirport } from '@/src/service/airport'
import { getFleet } from '@/src/service/fleet'
import { getFlight } from '@/src/service/flight'
import { getLegState } from '@/src/service/leg_state'
import { getNetwork } from '@/src/service/network'
import { syncFlight } from '@/src/service/sync'
import {
  closeDropdownViewColumn,
  closeFilterModal,
  openDropdownViewColumn,
  openFilterModal,
  setCheckListFlight,
  setParams,
  setSelectedFlightId,
  setSelectedPTSMasterId,
} from '@/src/store/ArrivalSlice'
import {
  openModalArrivalFlightDetail,
  setSelectedFlightModalId,
} from '@/src/store/ModalArrivalFlightSlice'
import {
  FilterFilled,
  ReloadOutlined,
  SearchOutlined,
  SyncOutlined,
} from '@ant-design/icons'
import { useMutation, useQuery } from '@tanstack/react-query'
import type { TableColumnsType } from 'antd'
import {
  Button,
  DatePicker,
  Flex,
  Form,
  Input,
  message,
  Popover,
  Radio,
  Select,
  Table,
  TimePicker,
} from 'antd'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import QueryString from 'qs'
import { lazy, Suspense, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useLocation, useNavigate, useSearchParams } from 'react-router'
import DropdownChangeColumn from '../../components/DropdownColumn'
import styles from './index.module.scss'
const ModalArrivalFlightDetail = lazy(
  () => import('@/src/pages/Arrival/components/ModalArrivalFlightDetail')
)
dayjs.extend(utc)
const { RangePicker } = DatePicker

const ArrivalPage = () => {
  const { t } = useTranslation()

  const { airportGlobal } = useAppSelector(state => state.global)

  const navigate = useNavigate()

  const [form] = Form.useForm()

  const [searchParams] = useSearchParams()
  const paramLocation = useLocation().search

  const [keyword, setKeyword] = useState<undefined | string>(undefined)
  const debouncedKeyword = useDebounce(keyword, 500)

  const [paramsArrival, setParamsArrival] = useState<{
    maxResultCount: number
    skipCount: number
    fromDate: string
    toDate: string
    fleetTypes: string[]
    legStates: string[]
    networks: string[]
    depApSched: string
    arrApSched: string
    keyWord: string
    fnCarrier: string
  }>(() => {
    const queryParams = QueryString.parse(paramLocation, {
      ignoreQueryPrefix: true,
    })

    const from =
      typeof queryParams.fromDate === 'string'
        ? dayjs(queryParams.fromDate)
        : null

    const to =
      typeof queryParams.toDate === 'string' ? dayjs(queryParams.toDate) : null

    const valid = isValidRangeDate(from, to)

    const value: any = {
      ...queryParams,
      skipCount: Number(queryParams.skipCount) || 0,
      maxResultCount: Number(queryParams.maxResultCount) || 1000,
      fromDate:
        valid && from
          ? from?.format(ISO_DATETIME)
          : dayjs().startOf('day').format(ISO_DATETIME),
      toDate:
        valid && to
          ? to?.format(ISO_DATETIME)
          : dayjs().endOf('day').format(ISO_DATETIME),
      fleetTypes: queryParams.fleetTypes || [],
      legStates: queryParams.legStates || [],
      networks: queryParams.networks || [],
      depApSched: queryParams.depApSched || '',
      arrApSched: airportGlobal,
      fnCarrier: queryParams.fnCarrier || '',
    }

    return value
  })

  const {
    selectedFlightId,
    visibleFilterModal,
    visibleDropdownViewColumn,
    checkListFlight,
  } = useAppSelector(state => state.arrival)

  const { params: airportParams } = useAppSelector(state => state.airport)

  const query = useQuery({
    queryKey: ['arrival', paramsArrival, airportGlobal],
    queryFn: () =>
      getFlight({
        ...paramsArrival,
        arrApSched: airportGlobal,
      }),
    enabled: !!airportGlobal,
    refetchInterval: 3 * 60 * 1000,
  })

  const { data: airportData } = useQuery({
    queryKey: ['get-airport-list', airportParams],
    queryFn: () =>
      getAirport({
        MaxResultCount: 1000,
      }),
    enabled: !!visibleFilterModal,
  })

  const { data: networkData } = useQuery({
    queryKey: ['get-network-list'],
    queryFn: () => getNetwork(),
    enabled: !!visibleFilterModal,
  })

  const { data: legStateData } = useQuery({
    queryKey: ['get-leg-state-list'],
    queryFn: () => getLegState(),
    enabled: !!visibleFilterModal,
  })

  const { data: fleetData } = useQuery({
    queryKey: ['get-fleet-list'],
    queryFn: () =>
      getFleet({
        MaxResultCount: 1000,
      }),
    enabled: !!visibleFilterModal,
  })

  const mutationSync = useMutation({
    mutationFn: async () => {
      return syncFlight()
    },
    onSuccess: () => {
      message.success(t('departure.syncSuccess'))
    },
    onError: (error: any) => {
      handleApiError(error)
    },
  })

  const dispatch = useAppDispatch()

  const columns: TableColumnsType<any> = [
    {
      key: 'index',
      title: t('table.order'),
      align: 'center',
      render: (_, __, index) => paramsArrival.skipCount + index + 1,
    },
    {
      key: 'registerNo',
      dataIndex: 'acRegistration',
      title: 'Register No',
      hidden: false,
      align: 'center',
    },
    {
      key: 'aircraft',
      dataIndex: 'acSubType',
      title: 'Aircraft',
      hidden: false,
      align: 'center',
    },
    {
      key: 'nature',
      dataIndex: 'fltType',
      title: 'Nature',
      hidden: false,
      align: 'center',
    },
    {
      key: 'flightNo',
      title: 'Flight No',
      hidden: false,
      render: (_value, record) => record.fnCarrier + record.fnNumber,
      align: 'center',
    },
    // {
    //   key: 'codeShare',
    //   dataIndex: 'codeShare',
    //   title: 'Code share',
    //   hidden: false,
    //   width: 100,
    //   align: 'center',
    // },
    {
      key: 'routing',
      title: 'Routing',
      hidden: false,
      render: (_value, record) => `${record.depApSched}-${record.arrApSched}`,
      align: 'center',
    },
    {
      key: 'sta',
      dataIndex: 'depSchedDt',
      title: 'STA',
      hidden: false,
      align: 'center',
      render: (value, record) => (
        <>{renderTimeWithSign(value, record.depSchedDt)}</>
      ),
    },
    {
      key: 'eta',
      dataIndex: 'depDt',
      title: 'ETA',
      hidden: false,
      align: 'center',
      render: (value, record) => (
        <>{renderTimeWithSign(value, record.depSchedDt)}</>
      ),
    },
    // {
    //   key: 'tda',
    //   dataIndex: 'depDt',
    //   title: 'TDA',
    //   hidden: false,
    //   align: 'center',
    //   render: value => <>{moment(value).format(ISO_DATETIME_NOSECOND)}</>,
    //   width: 150,
    // },
    {
      key: 'ata',
      dataIndex: 'depDt',
      title: 'ATA',
      hidden: false,
      align: 'center',
      render: (value, record) => (
        <>{renderTimeWithSign(value, record.depSchedDt)}</>
      ),
    },
    // {
    //   key: 'bay',
    //   dataIndex: 'bay',
    //   title: 'Bay',
    //   hidden: false,
    //   width: 60,
    // },
    // {
    //   key: 'belt',
    //   dataIndex: 'bay',
    //   title: 'Belt',
    //   hidden: false,
    //   width: 100,
    // },
    {
      key: 'nextFlight',
      title: 'Next Flight',
      hidden: false,
    },
    {
      key: 'status',
      dataIndex: 'legState',
      title: 'Status',
      hidden: false,
      align: 'center',
    },
    // {
    //   key: 'sabreCAPC',
    //   title: '1A CAPC',
    //   hidden: false,
    //   width: 100,
    //   align: 'center',
    // },
    // {
    //   key: 'booking',
    //   title: 'Booking',
    //   hidden: false,
    //   width: 100,
    //   align: 'center',
    // },
    // {
    //   key: 'onboardPax',
    //   dataIndex: 'ckiC',
    //   title: 'Onboard Pax',
    //   hidden: false,
    //   width: 100,
    //   align: 'center',
    // },
    {
      key: 'vip',
      dataIndex: 'vip',
      title: 'VIP',
      hidden: false,
      align: 'center',
      onCell: (record: any) => ({
        className: record.vip && record.vip1 ? 'bg-[#FB4E4E]' : '',
      }),
    },
    {
      key: 'remark',
      dataIndex: 'remark',
      title: 'Remark',
      hidden: false,
      width: 300,
    },
    // {
    //   key: 'eval',
    //   dataIndex: 'eval',
    //   title: 'Eval',
    //   hidden: false,
    //   width: 100,
    // },
    {
      key: 'onDuties',
      dataIndex: 'duties',
      title: 'On duties',
      hidden: false,
      align: 'center',
      width: 300,
    },
  ]

  const newColumns = columns.map(item => ({
    ...item,
    hidden: !checkListFlight.includes(item.key as string),
  }))

  const now = dayjs().startOf('hour')
  const start = now.subtract(1, 'hour')
  const end = now.add(3, 'hour')

  const renderTitleTable = () => {
    return (
      <div className="w-full flex justify-end">
        <div className="flex flex-row gap-x-2 mt-2">
          <Button
            type="primary"
            icon={<ReloadOutlined />}
            className="!text-sm !font-bold"
            onClick={() => query.refetch()}
            loading={query.isRefetching}
          >
            {t('departure.refresh')}
          </Button>
          <Button
            type="primary"
            icon={<SyncOutlined />}
            className="!text-sm !font-bold"
            loading={mutationSync.isPending}
            disabled={mutationSync.isPending}
            onClick={() => mutationSync.mutate()}
          >
            {t('departure.sync')}
          </Button>
          <TimePicker.RangePicker
            format="HH"
            className="!w-32"
            allowClear={false}
            needConfirm={false}
            defaultValue={[start, end]}
            onChange={(values: any) => {
              if (values) {
                const [start, end] = values
                console.log('Start:', start?.format('HH'))
                console.log('End:', end?.format('HH'))
                if (start && end) {
                  console.log('filter hour')
                }
              }
            }}
          />
          <Input
            prefix={<SearchOutlined />}
            placeholder={t('departure.searchPlaceholder')}
            className="!w-[200px]"
            defaultValue={searchParams.get('keyWord') || ''}
            onChange={e => {
              setKeyword(e.target.value)
            }}
          />
          <Popover
            open={visibleFilterModal}
            arrow={false}
            placement="bottomRight"
            trigger={['click']}
            content={
              <Form
                className="w-[500px]"
                form={form}
                layout="vertical"
                initialValues={{
                  rangeDate: [
                    dayjs(paramsArrival.fromDate),
                    dayjs(paramsArrival.toDate),
                  ],
                  depApSched: paramsArrival.depApSched,
                  arrApSched: airportGlobal,
                  network: paramsArrival.networks,
                  fleetType: paramsArrival.fleetTypes,
                  legState: paramsArrival.legStates,
                }}
              >
                <Form.Item name="rangeDate" label={t('departure.rangeDate')}>
                  <RangePicker
                    allowClear={false}
                    defaultValue={[
                      dayjs(paramsArrival.fromDate),
                      dayjs(paramsArrival.toDate),
                    ]}
                    format={DISPLAY_DATE}
                    className="w-full"
                  />
                </Form.Item>
                <Flex className="flex flex-row w-full gap-x-6">
                  <Form.Item
                    name="depApSched"
                    label={t('departure.depApSched')}
                    className="w-1/2"
                  >
                    <Select
                      allowClear
                      showSearch
                      filterOption={(input, option) =>
                        (option?.label ?? '')
                          .toLowerCase()
                          .includes(input.toLowerCase())
                      }
                      placeholder={t('common.all')}
                      options={[
                        { value: '', label: `${t('common.all')}` },
                        ...(airportData?.items.map((item: IAirport) => ({
                          value: item.iataCode,
                          label: item.iataCode,
                        })) || []),
                      ]}
                    />
                  </Form.Item>
                  <Form.Item
                    name="arrApSched"
                    label={t('departure.arrApSched')}
                    className="w-1/2"
                  >
                    <Select
                      allowClear
                      showSearch
                      className="pointer-events-none"
                      filterOption={(input, option) =>
                        (option?.label ?? '')
                          .toLowerCase()
                          .includes(input.toLowerCase())
                      }
                      value={airportGlobal}
                      placeholder={t('common.all')}
                      options={[
                        { value: null, label: `${t('common.all')}` },
                        ...(airportData?.items.map((item: IAirport) => ({
                          value: item.iataCode,
                          label: item.iataCode,
                        })) || []),
                      ]}
                    />
                  </Form.Item>
                </Flex>
                <Flex className="flex flex-row w-full gap-x-6">
                  <Form.Item
                    name="network"
                    label={t('departure.network')}
                    className="w-1/2"
                  >
                    <Select
                      mode="multiple"
                      allowClear
                      showSearch
                      filterOption={(input, option) =>
                        (option?.label ?? '')
                          .toLowerCase()
                          .includes(input.toLowerCase())
                      }
                      defaultValue={paramsArrival.networks}
                      placeholder={t('common.all')}
                      options={[
                        ...(networkData?.map((item: any) => ({
                          value: item.name,
                          label: item.name,
                        })) || []),
                      ]}
                    />
                  </Form.Item>
                  <Form.Item
                    name="fleetType"
                    label={t('departure.fleetType')}
                    className="w-1/2"
                  >
                    <Select
                      mode="multiple"
                      allowClear
                      showSearch
                      filterOption={(input, option) =>
                        (option?.label ?? '')
                          .toLowerCase()
                          .includes(input.toLowerCase())
                      }
                      placeholder={t('common.all')}
                      options={[
                        ...(fleetData?.items.map((item: any) => ({
                          value: item.code,
                          label: item.code,
                        })) || []),
                      ]}
                    />
                  </Form.Item>
                </Flex>
                <Form.Item
                  name="legState"
                  label={t('departure.legState')}
                  className="w-1/2 !pr-3"
                >
                  <Select
                    className="w-full"
                    mode="multiple"
                    allowClear
                    showSearch
                    filterOption={(input, option) =>
                      (option?.label ?? '')
                        .toLowerCase()
                        .includes(input.toLowerCase())
                    }
                    placeholder={t('common.all')}
                    options={[
                      ...(legStateData?.map((item: any) => ({
                        value: item.name,
                        label: item.name,
                      })) || []),
                    ]}
                  />
                </Form.Item>
                <Form.Item name="fnCarrier" label="">
                  <Radio.Group defaultValue={''}>
                    <Radio value="">All</Radio>
                    <Radio value="ov">OV</Radio>
                    <Radio value="bl">BL</Radio>
                    <Radio value="vn">VN</Radio>
                  </Radio.Group>
                </Form.Item>
                <div className="flex justify-end gap-x-2">
                  <Button
                    onClick={() => {
                      form.resetFields()
                      form.setFieldsValue({
                        rangeDate: [
                          dayjs().startOf('day'),
                          dayjs().endOf('day'),
                        ],
                      })
                      dispatch(closeFilterModal())
                      setParamsArrival({
                        ...paramsArrival,
                        fromDate: dayjs().startOf('day').format(ISO_DATETIME),
                        toDate: dayjs().endOf('day').format(ISO_DATETIME),
                        depApSched: '',
                        arrApSched: airportGlobal,
                        networks: [],
                        fleetTypes: [],
                        legStates: [],
                        fnCarrier: '',
                      })
                    }}
                  >
                    {t('common.cancel')}
                  </Button>
                  <Button
                    type="primary"
                    onClick={() => {
                      const depApSched = form.getFieldValue('depApSched')
                      const rangeDate = form.getFieldValue('rangeDate')
                      const newParams = {
                        ...paramsArrival,
                        depApSched,
                        arrApSched: airportGlobal,
                        skipCount: 0,
                        fromDate: dayjs(
                          rangeDate?.[0] || dayjs().startOf('day')
                        ).format(ISO_DATETIME),
                        toDate: dayjs(
                          rangeDate?.[1] || dayjs().endOf('day')
                        ).format(ISO_DATETIME),
                        networks: form.getFieldValue('network') || [],
                        fleetTypes: form.getFieldValue('fleetType') || [],
                        legStates: form.getFieldValue('legState') || [],
                        fnCarrier: form.getFieldValue('fnCarrier') || '',
                      }
                      setParamsArrival(newParams)
                      dispatch(closeFilterModal())
                    }}
                  >
                    {t('common.apply')}
                  </Button>
                </div>
              </Form>
            }
          >
            <Button
              icon={<FilterFilled />}
              onClick={() => {
                if (visibleFilterModal) {
                  dispatch(closeFilterModal())
                } else {
                  dispatch(openFilterModal())
                }
              }}
            >
              {t('common.filter')}
            </Button>
          </Popover>
          <DropdownChangeColumn
            columns={columns || []}
            onChangeColumn={val => dispatch(setCheckListFlight(val))}
            onOk={() => dispatch(openDropdownViewColumn())}
            onCancel={() => dispatch(closeDropdownViewColumn())}
            open={visibleDropdownViewColumn}
          />
        </div>
      </div>
    )
  }

  useEffect(() => {
    if (keyword !== undefined) {
      setParamsArrival({
        ...paramsArrival,
        skipCount: 0,
        keyWord: debouncedKeyword as string,
      })
    }
  }, [debouncedKeyword])

  useEffect(() => {
    dispatch(setCheckListFlight(columns.map(item => item.key as string)))
  }, [])

  useEffect(() => {
    navigate(
      `/flight-schedule/arrival?${QueryString.stringify(paramsArrival)}`,
      { replace: true }
    )
    dispatch(setParams(paramsArrival))
  }, [paramsArrival])

  useEffect(() => {
    setParamsArrival({
      ...paramsArrival,
      skipCount: 0,
    })
    dispatch(closeFilterModal())
    form.setFieldsValue({ arrApSched: airportGlobal })
  }, [airportGlobal])

  useEffect(() => {
    dispatch(setSelectedFlightId(''))
  }, [])

  return (
    <div className="flex flex-col gap-y-4">
      <Table
        columns={newColumns}
        bordered
        size="small"
        className={`${styles.whiteHeader}`}
        title={renderTitleTable}
        scroll={{ x: 'max-content' }}
        dataSource={query.data?.items || []}
        onRow={record => ({
          onClick: () => {
            dispatch(setSelectedFlightId(record.id))
            dispatch(setSelectedPTSMasterId(record.ptsId))
          },
          onDoubleClick: () => {
            dispatch(openModalArrivalFlightDetail())
            dispatch(setSelectedFlightModalId(record.id))
          },
        })}
        rowHoverable={false}
        rowClassName={record => {
          return record.id === selectedFlightId ? '!bg-[#E6F0F3]' : ''
        }}
        pagination={{
          total: query.data?.totalCount || 0,
          current: paramsArrival.skipCount / paramsArrival.maxResultCount + 1,
          pageSize: paramsArrival.maxResultCount,
          onChange: (page, pageSize) => {
            setParamsArrival({
              ...paramsArrival,
              skipCount: (page - 1) * pageSize,
              maxResultCount: pageSize,
            })
          },
          showSizeChanger: true,
          showTotal: (total, range) => (
            <ShowTotal total={total} range={range} />
          ),
        }}
        rowKey={record => record.id}
        loading={query.isLoading}
      />
      <Suspense>
        <ModalArrivalFlightDetail />
      </Suspense>
    </div>
  )
}

export default ArrivalPage
