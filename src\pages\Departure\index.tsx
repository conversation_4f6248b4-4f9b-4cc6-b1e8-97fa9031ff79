/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { DISPLAY_DATE, ISO_DATETIME } from '@/src/constants'
import { handleApiError } from '@/src/helper/handleApiError'
import { renderTimeWithSign } from '@/src/helper/renderTimeSign'
import { isValidRangeDate } from '@/src/helper/validDate'
import { warningCell } from '@/src/helper/warning'
import { useAppDispatch } from '@/src/hooks/useAppDispatch'
import { useAppSelector } from '@/src/hooks/useAppSelector'
import useDebounce from '@/src/hooks/useDebounce'
import type { IAirport } from '@/src/schema/IAirport'
import { getAirport } from '@/src/service/airport'
import { getFleet } from '@/src/service/fleet'
import { getFlight } from '@/src/service/flight'
import { getLegState } from '@/src/service/leg_state'
import { getNetwork } from '@/src/service/network'
import { syncFlight } from '@/src/service/sync'
import {
  closeDropdownViewColumn,
  closeFilterModal,
  openDropdownViewColumn,
  openFilterModal,
  setCheckListFlight,
  setParams,
  setSelectedFlightId,
  setSelectedPTSMasterId,
} from '@/src/store/DepartureSlice'
import {
  openModalDepartureFlightDetail,
  setSelectedFlightModalId,
} from '@/src/store/ModalDepartureFlightSlice'
import {
  DownloadOutlined,
  FilterFilled,
  ReloadOutlined,
  SearchOutlined,
  SyncOutlined,
} from '@ant-design/icons'
import { useMutation, useQuery } from '@tanstack/react-query'
import type {
  InputRef,
  TableColumnsType,
  TableColumnType,
  TableProps,
} from 'antd'
import {
  Button,
  DatePicker,
  Flex,
  Form,
  Input,
  message,
  Popover,
  Radio,
  Select,
  Space,
  Table,
  TimePicker,
} from 'antd'
import type { FilterDropdownProps } from 'antd/es/table/interface'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import QueryString from 'qs'
import { lazy, Suspense, useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useLocation, useNavigate, useSearchParams } from 'react-router'
import DropdownChangeColumn from '../../components/DropdownColumn'
import styles from './index.module.scss'
const ModalDepartureFlightDetail = lazy(
  () => import('@/src/pages/Departure/components/ModalDepartureFlightDetail')
)

type OnChange = NonNullable<TableProps<any>['onChange']>
type Filters = Parameters<OnChange>[1]

const legState_ARR = ['DEP', 'ARR', 'ON', 'OFF', 'IN', 'OUT']

dayjs.extend(utc)
const { RangePicker } = DatePicker

const DeparturePage = () => {
  const { t } = useTranslation()

  const { airportGlobal } = useAppSelector(state => state.global)

  const navigate = useNavigate()

  const [form] = Form.useForm()

  const [searchParams] = useSearchParams()
  const paramLocation = useLocation().search

  const [keyword, setKeyword] = useState<undefined | string>(undefined)
  const debouncedKeyword = useDebounce(keyword, 500)

  const [filteredInfo, setFilteredInfo] = useState<Filters>({})

  const [paramsDeparture, setParamsDeparture] = useState<{
    maxResultCount: number
    skipCount: number
    fromDate: string
    toDate: string
    fleetTypes: string[]
    legStates: string[]
    networks: string[]
    depApSched: string
    arrApSched: string
    keyWord: string
    fnCarrier: string
  }>(() => {
    const queryParams = QueryString.parse(paramLocation, {
      ignoreQueryPrefix: true,
    })

    const from =
      typeof queryParams.fromDate === 'string'
        ? dayjs(queryParams.fromDate)
        : null

    const to =
      typeof queryParams.toDate === 'string' ? dayjs(queryParams.toDate) : null

    const valid = isValidRangeDate(from, to)

    const value: any = {
      ...queryParams,
      skipCount: Number(queryParams.skipCount) || 0,
      maxResultCount: Number(queryParams.maxResultCount) || 1000,
      fromDate:
        valid && from
          ? from?.format(ISO_DATETIME)
          : dayjs().startOf('day').format(ISO_DATETIME),
      toDate:
        valid && to
          ? to?.format(ISO_DATETIME)
          : dayjs().endOf('day').format(ISO_DATETIME),
      fleetTypes: queryParams.fleetTypes || [],
      legStates: queryParams.legStates || [],
      networks: queryParams.networks || [],
      depApSched: airportGlobal,
      arrApSched: queryParams.arrApSched || '',
      fnCarrier: queryParams.fnCarrier || '',
    }

    return value
  })

  const {
    selectedFlightId,
    visibleFilterModal,
    visibleDropdownViewColumn,
    checkListFlight,
  } = useAppSelector(state => state.departure)

  const { params: airportParams } = useAppSelector(state => state.airport)

  const query = useQuery({
    queryKey: ['departure', paramsDeparture, airportGlobal],
    queryFn: () => {
      return getFlight({
        ...paramsDeparture,
        depApSched: airportGlobal,
      })
    },
    enabled: !!airportGlobal,
    refetchInterval: 3 * 60 * 1000,
  })

  const { data: airportData } = useQuery({
    queryKey: ['get-airport-list', airportParams],
    queryFn: () =>
      getAirport({
        MaxResultCount: 1000,
      }),
    enabled: !!visibleFilterModal,
  })

  const { data: networkData } = useQuery({
    queryKey: ['get-network-list'],
    queryFn: () => getNetwork(),
    enabled: !!visibleFilterModal,
  })

  const { data: legStateData } = useQuery({
    queryKey: ['get-leg-state-list'],
    queryFn: () => getLegState(),
  })

  const { data: fleetData } = useQuery({
    queryKey: ['get-fleet-list'],
    queryFn: () =>
      getFleet({
        MaxResultCount: 1000,
      }),
    enabled: !!visibleFilterModal,
  })

  const mutationSync = useMutation({
    mutationFn: async () => {
      return syncFlight()
    },
    onSuccess: () => {
      message.success(t('departure.syncSuccess'))
    },
    onError: (error: any) => {
      handleApiError(error)
    },
  })

  const dispatch = useAppDispatch()

  const [searchText, setSearchText] = useState('')
  const [searchedColumn, setSearchedColumn] = useState('')

  const searchInput = useRef<InputRef>(null)

  const handleSearch = (
    selectedKeys: string[],
    confirm: FilterDropdownProps['confirm'],
    dataIndex: any
  ) => {
    confirm()
    setSearchText(selectedKeys[0] as any)
    setSearchedColumn(String(dataIndex))
  }

  const handleReset = (
    clearFilters: () => void,
    confirm: FilterDropdownProps['confirm']
  ) => {
    clearFilters()
    setSearchText('')
    setSearchedColumn('')
    confirm()
  }

  const getHighlightedText = (text: string, highlight: string) => {
    if (!highlight.trim()) {
      return text
    }

    const regex = new RegExp(`(${highlight})`, 'gi')
    const parts = text.split(regex)

    return parts.map((part, index) =>
      regex.test(part) ? (
        <span
          key={index}
          style={{ backgroundColor: '#ffc069', padding: '0 2px' }}
        >
          {part}
        </span>
      ) : (
        part
      )
    )
  }

  const getFlightNoSearchProps = (): TableColumnType<any> => ({
    filterDropdown: ({
      setSelectedKeys,
      selectedKeys,
      confirm,
      clearFilters,
      close,
    }) => (
      <div className="p-2 flex flex-col" onKeyDown={e => e.stopPropagation()}>
        <Input
          ref={searchInput}
          placeholder="Search Flight No"
          value={selectedKeys[0]}
          onChange={e => {
            setSelectedKeys(e.target.value ? [e.target.value] : [])
          }}
          onPressEnter={() => {
            handleSearch(selectedKeys as string[], confirm, 'flightNo')
          }}
          className="!mb-2 block !w-full"
        />
        <Space>
          <Button
            type="primary"
            onClick={() => {
              handleSearch(selectedKeys as string[], confirm, 'flightNo')
            }}
            icon={<SearchOutlined />}
            size="small"
            style={{ width: 90 }}
          >
            Search
          </Button>
          <Button
            onClick={() => clearFilters && handleReset(clearFilters, confirm)}
            size="small"
            style={{ width: 90 }}
          >
            Reset
          </Button>
          <Button
            type="link"
            size="small"
            onClick={() => {
              close()
            }}
          >
            Close
          </Button>
        </Space>
      </div>
    ),
    filterIcon: (filtered: boolean) => (
      <SearchOutlined style={{ color: filtered ? '#006885' : undefined }} />
    ),
    onFilter: (value, record) => {
      const flightNo = record.fnCarrier + record.fnNumber
      const searchValue = (value as string).toLowerCase()

      if (!flightNo) {
        return false
      }

      const stringValue = String(flightNo).toLowerCase()
      return stringValue.includes(searchValue)
    },
    filterDropdownProps: {
      onOpenChange(open) {
        if (open) {
          setTimeout(() => searchInput.current?.select(), 100)
        }
      },
    },
  })

  const getColumnSearchProps = (dataIndex: any): TableColumnType<any> => ({
    filterDropdown: ({
      setSelectedKeys,
      selectedKeys,
      confirm,
      clearFilters,
      close,
    }) => (
      <div className="p-2 flex flex-col" onKeyDown={e => e.stopPropagation()}>
        <Input
          ref={searchInput}
          value={selectedKeys[0]}
          onChange={e => {
            setSelectedKeys(e.target.value ? [e.target.value] : [])
          }}
          onPressEnter={() => {
            handleSearch(selectedKeys as string[], confirm, dataIndex)
          }}
          className="!mb-2 block !w-full"
        />
        <Space>
          <Button
            type="primary"
            onClick={() => {
              handleSearch(selectedKeys as string[], confirm, dataIndex)
            }}
            icon={<SearchOutlined />}
            size="small"
            style={{ width: 90 }}
          >
            Search
          </Button>
          <Button
            onClick={() => clearFilters && handleReset(clearFilters, confirm)}
            size="small"
            style={{ width: 90 }}
          >
            Reset
          </Button>
          <Button
            size="small"
            onClick={() => {
              close()
            }}
          >
            close
          </Button>
        </Space>
      </div>
    ),
    filterIcon: (filtered: boolean) => (
      <SearchOutlined style={{ color: filtered ? '#006885' : undefined }} />
    ),
    onFilter: (value, record) => {
      return record[dataIndex]
        ? record[dataIndex]
            .toString()
            .toLowerCase()
            .includes((value as string).toLowerCase())
        : false
    },
    filterDropdownProps: {
      onOpenChange(open) {
        if (open) {
          setTimeout(() => searchInput.current?.select(), 100)
        }
      },
    },
  })

  const columns: TableColumnsType<any> = [
    {
      key: 'index',
      title: t('table.order'),
      align: 'center',
      render: (_, __, index) => paramsDeparture.skipCount + index + 1,
    },
    {
      key: 'acStatus',
      title: 'AC Status',
      dataIndex: 'legState',
      align: 'center',
      render: (status: string) => (status === 'ARR' ? 'Departed' : status),
      filters: legStateData?.map((item: any) => ({
        text: item.name,
        value: item.name,
      })),
      filteredValue: filteredInfo.acStatus || null,
      onFilter: (value, record) => record.legState.includes(value as string),
      ellipsis: true,
    },
    {
      key: 'acRegistration',
      dataIndex: 'acRegistration',
      title: 'Register No',
      hidden: false,
      align: 'center',
      filteredValue: filteredInfo.acRegistration || null,
      ...getColumnSearchProps('acRegistration'),
    },
    {
      key: 'acSubType',
      dataIndex: 'acSubType',
      title: 'AC',
      hidden: false,
      align: 'center',
      filteredValue: filteredInfo.acSubType || null,
      ...getColumnSearchProps('acSubType'),
    },
    {
      key: 'nature',
      dataIndex: 'fltType',
      title: 'Nature',
      hidden: false,
      align: 'center',
    },
    // {
    //   key: 'preFlight',
    //   title: 'Pre Flight',
    //   hidden: false,
    //   width: 100,
    //   align: 'center',
    //   dataIndex: 'preFlight',
    // },
    {
      key: 'flightNo',
      title: 'Flight No',
      hidden: false,
      align: 'center',
      render: (_value, record) => {
        const flightNo = record.fnCarrier + record.fnNumber
        const shouldHighlight = searchedColumn === 'flightNo' && searchText
        return shouldHighlight
          ? getHighlightedText(flightNo || '', searchText)
          : flightNo
      },
      onCell: record => ({
        style: {
          backgroundColor: warningCell(record.legState),
        },
      }),
      filteredValue: filteredInfo.flightNo || null,
      ...getFlightNoSearchProps(),
    },
    {
      key: 'codeShare',
      dataIndex: 'codeShare',
      title: 'Code share',
      hidden: false,
      align: 'center',
    },
    {
      key: 'routing',
      title: 'Routing',
      hidden: false,
      render: (_value, record) => `${record.depApSched}-${record.arrApSched}`,
      align: 'center',
    },
    {
      key: 'std',
      dataIndex: 'depSchedDt',
      title: 'STD',
      hidden: false,
      align: 'center',
      render: (value, record) => (
        <>{renderTimeWithSign(value, record.depSchedDt)}</>
      ),
    },
    {
      key: 'etd',
      dataIndex: 'depDt',
      title: 'ETD',
      hidden: false,
      align: 'center',
      render: (value, record) => (
        <>{renderTimeWithSign(value, record.depSchedDt)}</>
      ),
    },
    // {
    //   key: 'netd',
    //   title: 'NETD',
    //   hidden: false,
    //   width: 150,
    //   align: 'center',
    // },
    // {
    //   key: 'cls',
    //   title: 'CLS',
    //   hidden: false,
    //   width: 100,
    //   align: 'center',
    // },
    // {
    //   key: 'preBRD',
    //   title: 'PreBRD',
    //   hidden: false,
    //   width: 100,
    //   align: 'center',
    // },
    {
      key: 'BRD',
      title: 'BRD',
      hidden: false,
      align: 'center',
      dataIndex: 'brd',
    },
    {
      key: 'fht',
      title: 'FHT',
      hidden: false,
      align: 'center',
      dataIndex: 'fht',
      render: (value, record) => (
        <>{renderTimeWithSign(value, record.depSchedDt)}</>
      ),
    },
    // {
    //   key: 'fht_netd',
    //   title: 'FHT-NETD',
    //   hidden: false,
    //   width: 100,
    //   align: 'center',
    // },
    {
      key: 'ATD',
      title: 'ATD',
      hidden: false,
      align: 'center',
      dataIndex: 'depDt',
      render: (value: any, record: any) =>
        legState_ARR.includes(record.legState)
          ? renderTimeWithSign(value, record.depSchedDt)
          : null,
    },
    // {
    //   key: 'TOT',
    //   title: 'TOT',
    //   hidden: false,
    //   width: 100,
    //   align: 'center',
    // },
    // {
    //   key: 'bay',
    //   dataIndex: 'bay',
    //   title: 'Bay',
    //   hidden: false,
    //   width: 60,
    // },
    // {
    //   key: 'gate',
    //   title: 'Gate',
    //   hidden: false,
    //   width: 100,
    //   align: 'center',
    // },
    {
      key: 'ETA/ATA',
      title: 'ETA/ATA',
      hidden: false,
      align: 'center',
      dataIndex: 'arrDt',
      render: (value, record) => renderTimeWithSign(value, record.depSchedDt),
    },
    {
      key: 'TAT',
      title: 'TAT',
      hidden: false,
      align: 'center',
      dataIndex: 'tat',
      onCell: (record: any) => ({
        className: record.tat && record.tat < record.gt ? 'bg-[#FB4E4E]' : '',
      }),
    },
    // {
    //   key: 'CAT',
    //   title: 'CAT',
    //   hidden: false,
    //   width: 100,
    //   align: 'center',
    //   // onCell: () => ({
    //   //   className: 'blink-cell',
    //   // }),
    // },
    {
      key: 'status',
      dataIndex: 'legState',
      title: 'Status',
      hidden: false,
      align: 'center',
    },
    {
      key: 'acConfig',
      title: 'AC Config',
      hidden: false,
      align: 'center',
      render: (record: any) => (
        <>{`${record.cap}(${record.seatsC}J${record.seatsW}W${record.seatsY}Y)`}</>
      ),
    },
    // {
    //   key: '1ACAPC',
    //   title: '1A CAPC',
    //   hidden: false,
    //   width: 100,
    //   align: 'center',
    // },
    // {
    //   key: 'booking',
    //   title: 'Booking',
    //   hidden: false,
    //   width: 100,
    //   align: 'center',
    // },
    // {
    //   key: 'PLA/TTN/GLD',
    //   title: 'PLA/TTN/GLD',
    //   hidden: false,
    //   width: 100,
    //   align: 'center',
    // },
    // {
    //   key: 'MC',
    //   title: 'MC',
    //   hidden: false,
    //   width: 100,
    //   align: 'center',
    // },
    // {
    //   key: 'onboardPax',
    //   title: 'Onboard Pax',
    //   hidden: false,
    //   width: 100,
    //   align: 'center',
    // },
    // {
    //   key: 'SHFT',
    //   title: 'SHFT',
    //   hidden: false,
    //   width: 100,
    //   align: 'center',
    // },
    // {
    //   key: 'EstMeals',
    //   title: 'Est Meals',
    //   hidden: false,
    //   width: 100,
    //   align: 'center',
    // },
    // {
    //   key: 'ActMeals',
    //   title: 'Act Meals',
    //   hidden: false,
    //   width: 100,
    //   align: 'center',
    // },
    {
      key: 'vip',
      dataIndex: 'vip',
      title: 'VIP',
      hidden: false,
      align: 'center',
      onCell: (record: any) => ({
        className: record.vip && record.vip1 ? 'bg-[#FB4E4E]' : '',
      }),
    },
    // {
    //   key: 'PR',
    //   title: 'PR',
    //   hidden: false,
    //   width: 100,
    //   align: 'center',
    // },
    // {
    //   key: 'OPT',
    //   title: 'OPT',
    //   hidden: false,
    //   width: 100,
    //   align: 'center',
    // },
    // {
    //   key: 'GD',
    //   title: 'GD',
    //   hidden: false,
    //   width: 100,
    //   align: 'center',
    // },
    {
      key: 'remark',
      dataIndex: 'remark',
      title: 'Remark',
      hidden: false,
      width: 300,
    },
    // {
    //   key: 'eval',
    //   dataIndex: 'eval',
    //   title: 'Eval',
    //   hidden: false,
    //   width: 100,
    // },
    {
      key: 'onDuties',
      dataIndex: 'duties',
      title: 'On Duties',
      hidden: false,
      width: 300,
      align: 'center',
    },
    // {
    //   key: 'totalPassengers',
    //   title: 'Total passengers',
    //   hidden: false,
    //   width: 150,
    //   align: 'center',
    // },
    // {
    //   key: 'ABO',
    //   title: 'ABO',
    //   hidden: false,
    //   width: 100,
    //   align: 'center',
    //   // onCell: () => ({
    //   //   className: 'blink-cell',
    //   // }),
    //   render: () => <div className=""></div>,
    // },
  ]

  const newColumns = columns.map(item => ({
    ...item,
    hidden: !checkListFlight.includes(item.key as string),
  }))

  const now = dayjs().startOf('hour')
  const start = now.subtract(1, 'hour')
  const end = now.add(3, 'hour')

  const handleChange: OnChange = (_, filters) => {
    setFilteredInfo(filters)
  }

  const renderTitleTable = () => {
    return (
      <div className="w-full flex justify-end">
        <div className="flex flex-row gap-x-2 mt-2 items-center">
          <Button
            type="primary"
            icon={<ReloadOutlined />}
            className="!text-sm !font-bold"
            onClick={() => query.refetch()}
            loading={query.isRefetching}
          >
            {t('departure.refresh')}
          </Button>
          <Button
            type="primary"
            icon={<SyncOutlined />}
            className="!text-sm !font-bold"
            loading={mutationSync.isPending}
            disabled={mutationSync.isPending}
            onClick={() => mutationSync.mutate()}
          >
            {t('departure.sync')}
          </Button>
          <TimePicker.RangePicker
            format="HH:00"
            className="!w-42"
            allowClear={false}
            defaultValue={[start, end]}
            onChange={(values: any) => {
              if (values) {
                const [start, end] = values
                console.log('Start:', start?.format('HH'))
                console.log('End:', end?.format('HH'))
              }
            }}
          />
          <Input
            prefix={<SearchOutlined />}
            placeholder={t('departure.searchPlaceholder')}
            className="!w-[200px]"
            defaultValue={searchParams.get('keyWord') || ''}
            onChange={e => {
              setKeyword(e.target.value)
            }}
          />
          <Popover
            open={visibleFilterModal}
            arrow={false}
            placement="bottomRight"
            trigger={['click']}
            content={
              <Form
                className="w-[500px]"
                form={form}
                layout="vertical"
                initialValues={{
                  rangeDate: [
                    dayjs(paramsDeparture.fromDate),
                    dayjs(paramsDeparture.toDate),
                  ],
                  depApSched: airportGlobal,
                  arrApSched: paramsDeparture.arrApSched,
                  network: paramsDeparture.networks,
                  fleetType: paramsDeparture.fleetTypes,
                  legState: paramsDeparture.legStates,
                }}
              >
                <Form.Item name="rangeDate" label={t('departure.rangeDate')}>
                  <RangePicker
                    allowClear={false}
                    defaultValue={[
                      dayjs(paramsDeparture.fromDate),
                      dayjs(paramsDeparture.toDate),
                    ]}
                    format={DISPLAY_DATE}
                    className="w-full"
                  />
                </Form.Item>
                <Flex className="w-full gap-x-6">
                  <Form.Item
                    name="depApSched"
                    label={t('departure.depApSched')}
                    className="w-1/2"
                  >
                    <Select
                      allowClear
                      showSearch
                      className="pointer-events-none"
                      filterOption={(input, option) =>
                        (option?.label ?? '')
                          .toLowerCase()
                          .includes(input.toLowerCase())
                      }
                      placeholder={t('common.all')}
                      options={[
                        { value: null, label: t('common.all') },
                        ...(airportData?.items.map((item: IAirport) => ({
                          value: item.iataCode,
                          label: item.iataCode,
                        })) || []),
                      ]}
                    />
                  </Form.Item>
                  <Form.Item
                    name="arrApSched"
                    label={t('departure.arrApSched')}
                    className="w-1/2"
                  >
                    <Select
                      allowClear
                      showSearch
                      filterOption={(input, option) =>
                        (option?.label ?? '')
                          .toLowerCase()
                          .includes(input.toLowerCase())
                      }
                      placeholder={t('common.all')}
                      options={[
                        { value: '', label: t('common.all') },
                        ...(airportData?.items.map((item: IAirport) => ({
                          value: item.iataCode,
                          label: item.iataCode,
                        })) || []),
                      ]}
                    />
                  </Form.Item>
                </Flex>
                <Flex className="w-full gap-x-6">
                  <Form.Item
                    name="network"
                    label={t('departure.network')}
                    className="w-1/2"
                  >
                    <Select
                      mode="multiple"
                      allowClear
                      showSearch
                      defaultValue={paramsDeparture.networks}
                      filterOption={(input, option) =>
                        (option?.label ?? '')
                          .toLowerCase()
                          .includes(input.toLowerCase())
                      }
                      placeholder={t('common.all')}
                      options={[
                        ...(networkData?.map((item: any) => ({
                          value: item.name,
                          label: item.name,
                        })) || []),
                      ]}
                    />
                  </Form.Item>
                  <Form.Item
                    name="fleetType"
                    label={t('departure.fleetType')}
                    className="w-1/2"
                  >
                    <Select
                      mode="multiple"
                      allowClear
                      showSearch
                      filterOption={(input, option) =>
                        (option?.label ?? '')
                          .toLowerCase()
                          .includes(input.toLowerCase())
                      }
                      placeholder={t('common.all')}
                      options={[
                        ...(fleetData?.items.map((item: any) => ({
                          value: item.code,
                          label: item.code,
                        })) || []),
                      ]}
                    />
                  </Form.Item>
                </Flex>
                <Form.Item name="legState" label={t('departure.legState')}>
                  <Select
                    mode="multiple"
                    allowClear
                    showSearch
                    filterOption={(input, option) =>
                      (option?.label ?? '')
                        .toLowerCase()
                        .includes(input.toLowerCase())
                    }
                    placeholder={t('common.all')}
                    options={[
                      ...(legStateData?.map((item: any) => ({
                        value: item.name,
                        label: item.name,
                      })) || []),
                    ]}
                  />
                </Form.Item>
                <Form.Item name="fnCarrier" label={''}>
                  <Radio.Group defaultValue="">
                    <Radio value="">{t('common.all')}</Radio>
                    <Radio value="ov">OV</Radio>
                    <Radio value="bl">BL</Radio>
                    <Radio value="vn">VN</Radio>
                  </Radio.Group>
                </Form.Item>
                <div className="flex justify-end gap-x-2">
                  <Button
                    onClick={() => {
                      form.resetFields()
                      form.setFieldsValue({
                        rangeDate: [
                          dayjs().startOf('day'),
                          dayjs().endOf('day'),
                        ],
                      })
                      dispatch(closeFilterModal())
                      setParamsDeparture({
                        ...paramsDeparture,
                        depApSched: airportGlobal,
                        arrApSched: '',
                        networks: [],
                        fleetTypes: [],
                        legStates: [],
                        fnCarrier: '',
                        fromDate: dayjs().startOf('day').format(ISO_DATETIME),
                        toDate: dayjs().endOf('day').format(ISO_DATETIME),
                      })
                    }}
                  >
                    {t('common.cancel')}
                  </Button>
                  <Button
                    type="primary"
                    onClick={() => {
                      const rangeDate = form.getFieldValue('rangeDate')
                      const arrApSched = form.getFieldValue('arrApSched')
                      const newParams = {
                        ...paramsDeparture,
                        depApSched: airportGlobal,
                        arrApSched,
                        skipCount: 0,
                        fromDate: dayjs(
                          rangeDate?.[0] || dayjs().startOf('day')
                        ).format(ISO_DATETIME),
                        toDate: dayjs(
                          rangeDate?.[1] || dayjs().endOf('day')
                        ).format(ISO_DATETIME),
                        networks: form.getFieldValue('network') || [],
                        fleetTypes: form.getFieldValue('fleetType') || [],
                        legStates: form.getFieldValue('legState') || [],
                        fnCarrier: form.getFieldValue('fnCarrier') || '',
                      }
                      setParamsDeparture(newParams)
                      dispatch(closeFilterModal())
                    }}
                  >
                    {t('common.apply')}
                  </Button>
                </div>
              </Form>
            }
          >
            <Button
              icon={<FilterFilled />}
              onClick={() => {
                if (visibleFilterModal) {
                  dispatch(closeFilterModal())
                } else {
                  dispatch(openFilterModal())
                }
              }}
            >
              {t('common.filter')}
            </Button>
          </Popover>
          <Button icon={<DownloadOutlined />} className="!hidden">
            Download
          </Button>
          <DropdownChangeColumn
            columns={columns || []}
            onChangeColumn={val => dispatch(setCheckListFlight(val))}
            onOk={() => dispatch(openDropdownViewColumn())}
            onCancel={() => dispatch(closeDropdownViewColumn())}
            open={visibleDropdownViewColumn}
          />
        </div>
      </div>
    )
  }

  useEffect(() => {
    if (keyword !== undefined) {
      setParamsDeparture({
        ...paramsDeparture,
        skipCount: 0,
        keyWord: debouncedKeyword as string,
      })
    }
  }, [debouncedKeyword])

  useEffect(() => {
    dispatch(setCheckListFlight(columns.map(item => item.key as string)))
  }, [])

  useEffect(() => {
    navigate(
      `/flight-schedule/departure?${QueryString.stringify(paramsDeparture)}`,
      { replace: true }
    )
    dispatch(setParams(paramsDeparture))
  }, [paramsDeparture])

  useEffect(() => {
    setParamsDeparture({
      ...paramsDeparture,
      skipCount: 0,
    })
    dispatch(closeFilterModal())
    form.setFieldsValue({ depApSched: airportGlobal })
  }, [airportGlobal])

  useEffect(() => {
    dispatch(setSelectedFlightId(''))
  }, [])

  return (
    <div className="flex flex-col gap-y-4">
      <Table
        onChange={handleChange}
        columns={newColumns}
        bordered
        size="small"
        className={`${styles.whiteHeader}`}
        title={renderTitleTable}
        scroll={{ x: 'max-content' }}
        dataSource={query.data?.items || []}
        onRow={record => ({
          onClick: () => {
            dispatch(setSelectedFlightId(record.id))
            dispatch(setSelectedPTSMasterId(record.ptsId))
          },
          onDoubleClick: () => {
            dispatch(openModalDepartureFlightDetail())
            dispatch(setSelectedFlightModalId(record.id))
          },
        })}
        rowHoverable={false}
        rowClassName={record => {
          return record.id === selectedFlightId ? '!bg-[#E6F0F3]' : ''
        }}
        rowKey={record => record.id}
        loading={query.isLoading}
        // pagination={{
        //   total: query.data?.totalCount || 0,
        //   current:
        //     paramsDeparture.skipCount / paramsDeparture.maxResultCount + 1,
        //   pageSize: paramsDeparture.maxResultCount,
        //   onChange: (page, pageSize) => {
        //     setParamsDeparture({
        //       ...paramsDeparture,
        //       skipCount: (page - 1) * pageSize,
        //       maxResultCount: pageSize,
        //     })
        //   },
        //   showSizeChanger: true,
        //   showTotal: (total, range) => (
        //     <ShowTotal total={total} range={range} />
        //   ),
        // }}
        pagination={false}
      />
      <Suspense>
        <ModalDepartureFlightDetail />
      </Suspense>
    </div>
  )
}

export default DeparturePage
