export interface IFlightType {
  dataSource: string | null
  legNo: number
  acRegistration: string
  acLogicalNo: number
  fnCarrier: string
  fnNumber: string
  fnSufix: string
  seatsF: number
  seatsC: number
  seatsY: number
  cap: number
  flightMin: number
  flightHrs: number
  dayOfOrigin: string
  day: number
  acOwner: string
  acSubType: string
  acVersion: string
  depApSched: string
  arrApSched: string
  depApActual: string
  arrApActual: string
  depSchedDt: string
  arrSchedDt: string
  depDt: string
  arrDt: string
  legState: string
  legType: string
  lastModificationTime?: string
  lastModifierId?: string
  creationTime: string
  creatorId: string | null
  id: string
  ptsId: string
}

export interface IFlightParamsType {
  legNo?: number
  acRegistration?: string
  acSubtype?: string
  fnCarrier?: string
  fnNumber?: string
  arrApSched?: string
  legState?: string
  keyWord?: string
  fromDate?: string
  toDate?: string
  sorting?: string
  skipCount: number
  maxResultCount: number
  fleetTypes?: string[]
  legStates?: string[]
  network?: string[]
  depApSched?: string
  fromHour?: string
  toHour?: string
}
